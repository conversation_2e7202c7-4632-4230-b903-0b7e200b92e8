## 3.98.0
* [🎈 Pc1.21.8 (#1070)](https://github.com/PrismarineJS/minecraft-data/commit/7bfe410e80c5332074de6726dec8df6ad8c80ae1) (thanks @extremeheat)
* [Add 25w36b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/fdd13b6a4bd46a7a9de1c5de8a7cb7abf715960f) (thanks @github-actions[bot])
* [Add 25w36a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/4358ffe27a81ad4f7aa54f62b30217d54165d3ad) (thanks @github-actions[bot])
* [Fix update workflow](https://github.com/PrismarineJS/minecraft-data/commit/752a52d70ae2ad18bfdf01d8d7eac289f12807fa) (thanks @extremeheat)
* [Fix handle-mcpc-generator.yml (#1071)](https://github.com/PrismarineJS/minecraft-data/commit/063c333d8e60fe012a6734126479fce4a3324e35) (thanks @extremeheat)
* [Add pc 1.21.7 data (#1069)](https://github.com/PrismarineJS/minecraft-data/commit/1d6d9d408bf567e9a6f3fadd2aa079a50d8097d0) (thanks @Maks-gaming)
* [Add 25w35a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/549e5a83ea72193eaa6eac8462e01957af6f832f) (thanks @github-actions[bot])
* [Fix & cleanup the tints schema (#1056)](https://github.com/PrismarineJS/minecraft-data/commit/78d6e14a467fa719cb922e5bdc49275df4785860) (thanks @ItsDrike)
* [Handle dispatch from PrismarineJS/minecraft-data-generator (#1063)](https://github.com/PrismarineJS/minecraft-data/commit/c903b774986cd025950df3a9240d8fade1885070) (thanks @extremeheat)
* [Fix commands schema & enforce no additional properties (#1066)](https://github.com/PrismarineJS/minecraft-data/commit/5c47e32b57981d30cf9c5c15ac75633253680ea9) (thanks @ItsDrike)
* [Disallow additional properties in blockMappings schema (#1067)](https://github.com/PrismarineJS/minecraft-data/commit/de073027b68530fc0dd6de2ec921386a839145f7) (thanks @ItsDrike)
* [Ensure consistency between versions.json and dataPaths.json (#1068)](https://github.com/PrismarineJS/minecraft-data/commit/3075b6e8ad0778dfe134122ad65cb2f8f69632f3) (thanks @ItsDrike)
* [Add 25w34b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/46e60659d98b922f503c6b8f986564d5d38985ea) (thanks @github-actions[bot])
* [Add 25w34a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/0e3d40eb9a8c03c46b16fea594a30d4daa8a98a1) (thanks @github-actions[bot])

## 3.97.0
* [Revert "Fix pc "nbtNameForEnchant" feature versions (#986)"](https://github.com/PrismarineJS/minecraft-data/commit/34a88b5f770eb9f2b3d6a4ae8a5ec6a71b235327) (thanks @rom1504)

## 3.96.0
* [Update proto.yml for Minecraft 1.21.6 (#1039)](https://github.com/PrismarineJS/minecraft-data/commit/09821c14cc7ced37ac2e2554bc6bc0f84c4390c7) (thanks @rom1504)
* [Enforce no additional properties for enchantments (#1053)](https://github.com/PrismarineJS/minecraft-data/commit/c7509e277a28753663e5f48433f146eb17773f0d) (thanks @ItsDrike)
* [Add 25w33a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/a30c6c4732864d4c30b14e73f7732ce0f15406a3) (thanks @github-actions[bot])
* [Fix biomes schema validation issues (#1049)](https://github.com/PrismarineJS/minecraft-data/commit/31e0a3f2e7df3ae932fa39159028e52be402937b) (thanks @ItsDrike)
* [Make items schema strict and document missing fields (#1052)](https://github.com/PrismarineJS/minecraft-data/commit/db4139e7812e998220178d010cda73053e40f75c) (thanks @ItsDrike)

## 3.95.1
* [Fix features schema inconsistencies and validation issues (#1047)](https://github.com/PrismarineJS/minecraft-data/commit/c949824c7a34c0f8a5ed2b3d0d3240a964eeadd5) (thanks @rom1504)
* [Fix bedrock 1.21.100 packet_correct_player_move_prediction (#1044)](https://github.com/PrismarineJS/minecraft-data/commit/b6a81e62cf37e0eb9fdfb3477782219ff6b85adb) (thanks @TSL534)

## 3.95.0
* [Add bedrock 1.21.100 protocol data (#1041)](https://github.com/PrismarineJS/minecraft-data/commit/ae4cf26bd9a73478b6129ea404bef40db5e9f03e) (thanks @extremeheat)
* [Fix pc "nbtNameForEnchant" feature versions (#986)](https://github.com/PrismarineJS/minecraft-data/commit/79ad67106787847f05faa68aaca3f3faea5cf9a1) (thanks @IKorzI)
* [Mention minebase wrapper lib in readme (#1040)](https://github.com/PrismarineJS/minecraft-data/commit/24a38bc7746a5c280dad0c18567bd03c4afcec9f) (thanks @ItsDrike)
* [Add 25w32a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/820cdf0c515422b30805fc91ff292861d5152aae) (thanks @github-actions[bot])

## 3.94.0
* [Add pc 1.21.5 protocol data (#1029)](https://github.com/PrismarineJS/minecraft-data/commit/6d6e431dd22cd9d0fb9e8c5b7029f6f1311f7501) (thanks @extremeheat)
* [Add 25w31a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/60bc124b73d6daa12e80179e963aa93a80a40d45) (thanks @github-actions[bot])
* [Add bedrock 1.21.90 data (#1027)](https://github.com/PrismarineJS/minecraft-data/commit/e72f74ee618659bbbb15bfa97ae1c5fb194b4ad6) (thanks @bedrock-bot)
* [Add 1.21.8 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/05369ec2ec1f1f927359e9c4e90c8346111ac1a8) (thanks @github-actions[bot])
* [Add 1.21.8-rc1 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/a333d737118f65f6161ebdd353b36481d168b5fd) (thanks @github-actions[bot])

## 3.93.0
* [Add bedrock v1.21.93 to versions (#1032)](https://github.com/PrismarineJS/minecraft-data/commit/015c1c53fd4595c2226f729cb019d53514c91c32) (thanks @CreeperG16)
* [manually update some of 1.21.4 materials.json (#1033)](https://github.com/PrismarineJS/minecraft-data/commit/8dcb6a1b09777ef34a8db8dde879d2a65de4e65c) (thanks @Gjum)
* [fix: typo in SlotDisplay `smithing_trim` (#1031)](https://github.com/PrismarineJS/minecraft-data/commit/8071a1626422ddd3e9a8ab971edd9337f7b6d702) (thanks @patyhank)

## 3.92.0
* [PC protocol updates and refactor (#948)](https://github.com/PrismarineJS/minecraft-data/commit/1a0bb5380450a3b1559af47b48fb9be772531741) (thanks @extremeheat)
* [add 1.21.6 data (#1028)](https://github.com/PrismarineJS/minecraft-data/commit/724b1b1d1de57b2b4e4da42a6cf9a5bd2bbd4cae) (thanks @bedrock-bot)
* [doc: Add optional example in protocol.md](https://github.com/PrismarineJS/minecraft-data/commit/75c7e93be0ce3d2810f9bcb4d2d234aec6049496) (thanks @extremeheat)
* [Add 1.21.7 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/97efb66f1b4d5e42806d4ded033727638df90525) (thanks @github-actions[bot])

## 3.91.0
* [Add missing data version values in protocol version file (#1024)](https://github.com/PrismarineJS/minecraft-data/commit/72533f88da564797d59b618de59835c7984a79d5) (thanks @rom1504)
* [Add some 1.21.5 data (#995)](https://github.com/PrismarineJS/minecraft-data/commit/f9ea32fedfb44b8cc4b41c3b41e178a96c690907) (thanks @RomainNeup)
* [Add 1.21.7-rc2 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/0790f31720fffa6c65f04ce5fd5da12bbcf4f31b) (thanks @github-actions[bot])
* [Add 1.21.7-rc1 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/bfa42528b71ae2941ca92015e65be3c0cbcc1ddf) (thanks @github-actions[bot])

## 3.90.0
* [Add bedrock 1.21.90 protocol data (#1021)](https://github.com/PrismarineJS/minecraft-data/commit/06c2106a669deb24f0c14770874a0abeb116687b) (thanks @CreeperG16)
* [bedrock: add v1.21.70 Steve Skin (#1004)](https://github.com/PrismarineJS/minecraft-data/commit/b1b05bb62907ab0c6508d6f52e44b753df065d54) (thanks @yowzaoppo)
* [Add Bedrock 1.21.80 data (#1012)](https://github.com/PrismarineJS/minecraft-data/commit/23eff009e11d739dd2f2bfc782a440dc885ef7f3) (thanks @bedrock-bot)
* [pc: fix editBookPacketUsesNbt feature version range (#1013)](https://github.com/PrismarineJS/minecraft-data/commit/52b59d8f13e9b8e8e29654b95ca7e00ba702464f) (thanks @autowert66)

## 3.89.0
* [Add some sound features](https://github.com/PrismarineJS/minecraft-data/commit/431245e650d88863f867b156a6b418b56797658b) (thanks @rom1504)

## 3.88.0
* [Add title features.](https://github.com/PrismarineJS/minecraft-data/commit/33ac15edaa7da6c4252b52fc8f3c05803437dd64) (thanks @rom1504)

## 3.87.0
* [Fix bug in packet experience.](https://github.com/PrismarineJS/minecraft-data/commit/a32f3880296467fbed19e5205b1d1d23331fffa8) (thanks @rom1504)
* [Add 25w20a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/9ba5dfb80f37415e2ac79c3dc15c5581d5738bfa) (thanks @github-actions[bot])

## 3.86.0
* [Add Bedrock 1.21.80 protocol data (#1010)](https://github.com/PrismarineJS/minecraft-data/commit/9d518a1e4387c0323e55fe58dd2edbf50f6a13d8) (thanks @extremeheat)
* [Add data for bedrock 1.21.60 and 1.21.70 (#997)](https://github.com/PrismarineJS/minecraft-data/commit/57e34f79c2e9deac2e71888ba639f4cb01068c34) (thanks @bedrock-bot)
* [bedrock: add code_builder packet to protocol (#1000)](https://github.com/PrismarineJS/minecraft-data/commit/e2debd1bca378c38b75c7fd2de0796ab7087cc63) (thanks @EntifiedOptics)
* [Fix Bedrock protocol missing debug_info packet (#999)](https://github.com/PrismarineJS/minecraft-data/commit/c22521517fc708f387af874bd2ce603ae314541b) (thanks @EntifiedOptics)

## 3.85.0
* [Add Bedrock 1.21.70 (785) protocol data (#990)](https://github.com/PrismarineJS/minecraft-data/commit/982946615de283af7709ccf2611844d3808a63b7) (thanks @CreeperG16)
* [Fix bedrock ItemStackRequest packet fields (#988)](https://github.com/PrismarineJS/minecraft-data/commit/41aa5c7ed620a4b6b51892153fb8abe67b6ded8e) (thanks @EntifiedOptics)
* [Add bedrock 1.21.42 data (#992)](https://github.com/PrismarineJS/minecraft-data/commit/82049f691f9f8475b3871e6a4fb2ff7b5653959c) (thanks @bedrock-bot)
* [Add bedrock 1.21.0-1.21.60 block state data (#989)](https://github.com/PrismarineJS/minecraft-data/commit/6695a165f0c329affc2789bf7d7a428e21d98b68) (thanks @bedrock-bot)

## 3.84.1
* [bedrock: fix aim assist presets packet (#983)](https://github.com/PrismarineJS/minecraft-data/commit/99b6fb48a9e227a8dcb4234727eece94c95169e0) (thanks @extremeheat)
* [Add 25w07a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/97d62e48225c8a8953618c050b237677b3767e42) (thanks @github-actions[bot])

## 3.84.0
* [Add bedrock 1.21.60 data (#980)](https://github.com/PrismarineJS/minecraft-data/commit/641f42d548595b754c63248d25decc86caed8f21) (thanks @CreeperG16)
* [Add 25w06a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/c42c06e7965f7297829b1f946ced8b42723e61a5) (thanks @github-actions[bot])
* [Add 25w05a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/bcc43071da36dfd21e5ff084b5b08b3d914e06c7) (thanks @github-actions[bot])
* [node 22 (#978)](https://github.com/PrismarineJS/minecraft-data/commit/10749e0a06563f0cd563327e85c003b145923648) (thanks @rom1504)
* [Add 25w04a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/ebd71f1e8986c32eb235669312072bb54d90b23a) (thanks @github-actions[bot])
* [Fix bedrock packet_spawn_particle_effect (#976)](https://github.com/PrismarineJS/minecraft-data/commit/dd4d29d41f938d539371445a7dd897fd4fd12d36) (thanks @extremeheat)
* [Add 25w03a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/e6bbfbd4e4200ed363f7ec065764878f4b941636) (thanks @github-actions[bot])

## 3.83.1
* [Update pc1.21.4 entity metadata (#969)](https://github.com/PrismarineJS/minecraft-data/commit/0f8ead7370ea6fb11552c29dbb9d86fd4da8e4f2) (thanks @extremeheat)

## 3.83.0
* [Add pc1.21.4 data (#967)](https://github.com/PrismarineJS/minecraft-data/commit/d7c753a903c5cfee967db04b7f4b146ba8f8d375) (thanks @extremeheat)
* [Add 25w02a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/5f8a3f5df832c117c00447f8cec66d10795771c3) (thanks @github-actions[bot])

## 3.82.0
* [Add pc1.21.3 features (#965)](https://github.com/PrismarineJS/minecraft-data/commit/2ce91b425f91a6bac16d679bf7912974f768ca16) (thanks @extremeheat)
* [bedrock: fix protocol ItemStackRequest optional type, add 1.21.50 updated skin (#962)](https://github.com/PrismarineJS/minecraft-data/commit/6e12664f6bd49b62546b77e667c91b3ceeae6035) (thanks @yowzaoppo)

## 3.81.2
* [pc1.21.3 protocol: fix slot itemId name and player_info bitfield (#963)](https://github.com/PrismarineJS/minecraft-data/commit/9de94fd41d2577d592eafb391a2bcfefd761b6fa) (thanks @extremeheat)

## 3.81.1
* [Add pc 1.21.1 and 1.21.3 loginPackets (#960)](https://github.com/PrismarineJS/minecraft-data/commit/ae2828606ba66d77365fc8ccba3f1e4b56aa3be2) (thanks @extremeheat)

## 3.81.0
* [fixed incorrect 1.20.5 and 1.21.1 data (#943)](https://github.com/PrismarineJS/minecraft-data/commit/a72613c08b1a34d331cf7a62da758deb878a22cc) (thanks @Madlykeanu)
* [Update bedrock-ci.yml to install libssl v1.1](https://github.com/PrismarineJS/minecraft-data/commit/42da8a3217eeb1b34a7a54111337bebdf13abc5f) (thanks @extremeheat)

## 3.80.1
* [pc: Add 1.19.4 loginPacket and `removedNamedSoundEffectPacket` 1.19.3+ feature (#956)](https://github.com/PrismarineJS/minecraft-data/commit/4aae3c344149e844664fa0a0a8ba4fb3f3a94f52) (thanks @extremeheat)
* [Fix bedrock 1.21.50 protocol input flags (#952)](https://github.com/PrismarineJS/minecraft-data/commit/ea066f38c91be23f28a3cbc9fa0c6407c09b21b6) (thanks @extremeheat)

## 3.80.0
* [Add bedrock 1.21.50 protocol data (#945)](https://github.com/PrismarineJS/minecraft-data/commit/b0c5492bc709673ba684e2fc0fe034ff069f9c50) (thanks @CreeperG16)
* [bedrock: fix serverbound_loading_screen malformed packet (#944)](https://github.com/PrismarineJS/minecraft-data/commit/9753666c64c00d1307e8ad93a3f1e1d74f3c7586) (thanks @JSbETms)
* [Add 1.21.3 to readme](https://github.com/PrismarineJS/minecraft-data/commit/6dc131a5b286da399ad0201ba977e7766cd3a5f6) (thanks @rom1504)

## 3.79.0
* [Java 1.21.3 Support (#936)](https://github.com/PrismarineJS/minecraft-data/commit/2442f47dc991c587c8090a9469170dd86cfa0ee4) (thanks @GroobleDierne)
* [Add 1.21.4 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/f88f2699c43dc48a12a2bdf5fd0109dc0ebc94b7) (thanks @github-actions[bot])
* [Add 1.21.4-rc3 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/e0e23d27777f5e9d64d9797fd5dd69616875cf63) (thanks @github-actions[bot])
* [Add 1.21.4-rc2 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/564c0a6c1765d1462a33b2f65f74c77bff50144c) (thanks @github-actions[bot])
* [Add 1.21.4-rc1 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/ccbaf7685dc1239ffbd2129a66ae76ea276d8bb4) (thanks @github-actions[bot])
* [Add 1.21.4-pre3 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/bb89a33f423dab1bf6f64a59aad12c547c369a99) (thanks @github-actions[bot])
* [Add 1.21.4-pre2 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/42a2f3806fd6a22f8ed79f98fb0757f906cf0266) (thanks @github-actions[bot])
* [Add 1.21.4-pre1 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/9cfa6ea955af24d6f481484ebe5d8221a2265421) (thanks @github-actions[bot])
* [Add 24w46a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/dd1c067ce05464831dd2855a06cf0ed74bc48cd0) (thanks @github-actions[bot])
* [Add 24w45a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/27e699e134a5dcc39c3a94ae7735d27f8ea3040f) (thanks @github-actions[bot])
* [Add 24w44a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/fc60c49b07c937cb31d617b7ec7e6abcf78a8ce8) (thanks @github-actions[bot])
* [Add 1.21 data (protocol compliant w/ 1.21.1) (#935)](https://github.com/PrismarineJS/minecraft-data/commit/f1130aea931b948d2ecaecf34ecfe0116bfd4172) (thanks @GroobleDierne)

## 3.78.0
* [Add pc 1.21.1 data (#925)](https://github.com/PrismarineJS/minecraft-data/commit/d56346a4e32defcd28d9ba6750957c12001e78d6) (thanks @extremeheat)
* [Add incrementedChatType feature (#932)](https://github.com/PrismarineJS/minecraft-data/commit/8ef58b15fec74c6a276d0c83cfc741d6fb4e072a) (thanks @SuperGamerTron)

## 3.77.0
* [Add bedrock 1.21.42 protocol data (#930)](https://github.com/PrismarineJS/minecraft-data/commit/dee9f10b667a31d7bef66e7c5b6c9bdc9d6a7232) (thanks @CreeperG16)

## 3.76.0
* [add has execute command feature](https://github.com/PrismarineJS/minecraft-data/commit/20f34ae536e30370ecdb6fde6412ddba4d67d9a0) (thanks @rom1504)

## 3.75.0
* [use login data of 1.20.5 in 1.20.6](https://github.com/PrismarineJS/minecraft-data/commit/b5d1b0bfa18d3c9f41d289c285d2b54827f22004) (thanks @rom1504)

## 3.74.0
* [update recipes for 1.20.6](https://github.com/PrismarineJS/minecraft-data/commit/636d3a7e027e75ab1deeb4cd214b73306a836473) (thanks @rom1504)

## 3.73.0
* [add missing 1.20.6 in versions.json](https://github.com/PrismarineJS/minecraft-data/commit/214ae40d2d2c383cf5ef5050529be1693961930b) (thanks @rom1504)

## 3.72.0
* [Add 1.20.6 support assuming no change from 1.20.5.](https://github.com/PrismarineJS/minecraft-data/commit/9e9e84df7890b1a59f2f074fb1b0c0eb0885ca74) (thanks @rom1504)

## 3.71.0
* [Fix protocol.json array count after protodef yaml fix.](https://github.com/PrismarineJS/minecraft-data/commit/7bae37d8bb4a367a38395b30393c9df62e839984) (thanks @rom1504)
* [Add 1.20.5 in version list in readme](https://github.com/PrismarineJS/minecraft-data/commit/7f34231c97b2af16478abb2b66d9ecbc8e053d63) (thanks @rom1504)

## 3.70.0
* [Pc1.20.5 protocol (#898)](https://github.com/PrismarineJS/minecraft-data/commit/a240b84d2690f1c05d2a64a8bf1b152b3682ded5) (thanks @extremeheat)
* [Add 1.21.2-pre3 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/34bba2ce95af635a5d5e79359009fa779c135d42) (thanks @github-actions[bot])
* [Add 1.21.2-pre2 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/14bde312bffedda58b6cd59a868be9039bd7143e) (thanks @github-actions[bot])
* [Add 1.21.2-pre1 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/61c6b9ac56dd00f2881b1960455c8208df2169db) (thanks @github-actions[bot])
* [Add 24w40a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/c356b8cf02c71f9583ef0173e9281d83238d62bb) (thanks @github-actions[bot])
* [Add 24w39a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/9c8c31f2cee73500130e14e398a4b6ac6d5f22b8) (thanks @github-actions[bot])

## 3.69.0
* [1.21.30 support (#911)](https://github.com/PrismarineJS/minecraft-data/commit/b8282714565b2405ac946d72840239f88f9edb28) (thanks @pokecosimo)
* [[bedrock] Add missing 1.21.20 packets (#908)](https://github.com/PrismarineJS/minecraft-data/commit/aa82dcc0cd5afe0a8feebf8ac626c0836b98b929) (thanks @MrDiamond64)

## 3.68.1
* [Fix dataPaths.json for bedrock 1.21.2](https://github.com/PrismarineJS/minecraft-data/commit/d196f9208db6844e584d17f00d80379ab81eada0) (thanks @extremeheat)

## 3.68.0
* [Add bedrock 1.21.20 protocol data (#902)](https://github.com/PrismarineJS/minecraft-data/commit/854446ec2b05cab0d252469e4d94c43492019f3e) (thanks @extremeheat)
* [Implement protodef-yaml for mcpc (#886)](https://github.com/PrismarineJS/minecraft-data/commit/a748994fe231c356ffece37c533bcda5816b3d7a) (thanks @extremeheat)

## 3.67.0
* [Add bedrock 1.21.2 protocol data (#895)](https://github.com/PrismarineJS/minecraft-data/commit/35a8033e1ad4daba7e0d1c9a3112ab0b882251d6) (thanks @CreeperG16)
* [Correction to bedrock ItemStackRequests packet auto craft (#894)](https://github.com/PrismarineJS/minecraft-data/commit/ddd0700d1fe0c654a9f889b7263eeeaba48b0511) (thanks @extremeheat)
* [Bedrock 1.21.0 blocks data (#892)](https://github.com/PrismarineJS/minecraft-data/commit/d84b2283dc64fb246d6239e4b9fa8487c6f420eb) (thanks @bedrock-bot)
* [Add some 1.20.5 data (#879)](https://github.com/PrismarineJS/minecraft-data/commit/4ca8db1dd3d45302de3c33d01cfcc75464afdba1) (thanks @qwqtoday)

## 3.66.0
* [Add Bedrock 1.21.0 protocol data (#882)](https://github.com/PrismarineJS/minecraft-data/commit/67a392d204775473ce28b37db0bcfa01218624bb) (thanks @CreeperG16)
* [Add pc 1.18+ Book editing feature (#876)](https://github.com/PrismarineJS/minecraft-data/commit/af0ce69e3c212c1bd06e544b94049ae8bf11f321) (thanks @unlimitedcoder2) 
* [Update steve skin for bedrock (#880)](https://github.com/PrismarineJS/minecraft-data/commit/141dc91ad21b0713bd5ca56b7f79aa4874a3c128) (thanks @yowzaoppo)

## 3.65.0
* [Add bedrock 1.20.80 protocol data (#873)](https://github.com/PrismarineJS/minecraft-data/commit/7c216d876417eb3fe27c317700b38f0e638ee47a) (thanks @extremeheat)
* Update pc protocolVersions.json with 1.20.5 and new snapshots
* [Add bedrock 1.19.80-1.20.71 blocks data (#864)](https://github.com/PrismarineJS/minecraft-data/commit/e34bced290112d8fe7c3935369eec969b8453a71) (thanks @FreezeEngine)
* [Update bedrock protocol to fix `command_executed` and `player_movement_corrected` typos (#871)](https://github.com/PrismarineJS/minecraft-data/commit/0f86acc3b91279743fcc991ad2c9c313b8ebda57) (thanks @kotinash)

## 3.64.1
* [Update bedrock features.json to add `blockHashes`](https://github.com/PrismarineJS/minecraft-data/commit/bf994a81894ab47cf031ecdc9dff7165ac89e56e) (thanks @extremeheat)
* [Add 24w12a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/8a914067da3998bc8b84f41c33e3cf5fb8980599) (thanks @github-actions[bot])

## 3.64.0
* [Fixed 1.20.3 and 1.20.4 loginPacket version was set incorrectly on data/dataPaths.json (#853)](https://github.com/PrismarineJS/minecraft-data/commit/73679065860242acb6b8bf2692718700a1bd5c37) (thanks @n-qber)
* [Added a resource pack UUID feature that 1.20.3 and later versions use. (#854)](https://github.com/PrismarineJS/minecraft-data/commit/90d6ddac514232e2b609ad6f71012d052210a833) (thanks @TerminalCalamitas)
* [Add sounds files for majority of versions. (#839)](https://github.com/PrismarineJS/minecraft-data/commit/d9f4a0654fa980423ba4f182e443df15e70d018e) (thanks @wgaylord)
* [Create ARCHITECTURE.md (#856)](https://github.com/PrismarineJS/minecraft-data/commit/03107f89d1d70df42f568fc4a2da33af48404d38) (thanks @extremeheat)
* [Add 24w11a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/fe8ed241a4b141ed1fae58dd361597c9ee4581d9) (thanks @github-actions[bot])

## 3.63.0
* [Add bedrock 1.20.71 protocol data (#858)](https://github.com/PrismarineJS/minecraft-data/commit/fc0aac53acb683cba83f3d692cbf053094f49108) (thanks @extremeheat)
* [Add 24w10a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/97b1bafc5aab20bdff0bd0b4422ce868530b8806) (thanks @github-actions[bot])
* [Add 24w09a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/882319614133edc9fc7a93649e285f752a303401) (thanks @github-actions[bot])

## 3.62.0
* [Fix 1.20.2 and 1.20.3 Recipes, change docs to say to use Burger inste… (#843)](https://github.com/PrismarineJS/minecraft-data/commit/d47396ed1e8962fa0310bb603fc3ff4a4521b4f4) (thanks @wgaylord)

## 3.61.2
* [Add style to command param type enum in pc 1.20.3 protocol (#850)](https://github.com/PrismarineJS/minecraft-data/commit/4acaa9bd255951873eb11b12aca15a1079680d67) (thanks @extremeheat)

## 3.61.1
* [[bedrock] Fix issue with player auth input data (#848)](https://github.com/PrismarineJS/minecraft-data/commit/985b2bef89e9642da1be90b825969a4818d5e5e8) (thanks @MrDiamond64)
* [Add 24w07a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/4bf2ae5ccb7da0dc51898dbdd85cfbd04621fe07) (thanks @github-actions[bot])

## 3.61.0
* [Add bedrock 1.20.61 protocol data (#845)](https://github.com/PrismarineJS/minecraft-data/commit/b38e5da05d345d3c3af52466cf74167a94b1bb1a) (thanks @extremeheat)
* [Add 24w06a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/5630ff7074903a6638dd6595315888d9585d0e59) (thanks @github-actions[bot])
* [Add 24w05b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/ff68bc8540e582042af86edc5bf8ca98f8017f1d) (thanks @github-actions[bot])
* [Add 24w05a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/f2bbe7b57103d4c0c274a6dbc06f34e1c3638fc1) (thanks @github-actions[bot])

## 3.60.0
* [Fix spelling on packet_configuration_acknowledged in all locations in 1.20.2 protocol.json (#841)](https://github.com/PrismarineJS/minecraft-data/commit/4b0ac882a3900525dbf6cc0f0a4ffb9234d3b029) (thanks @wgaylord)
* [Add New Data Version - Doc update (#835)](https://github.com/PrismarineJS/minecraft-data/commit/c1b5113fc402966550050c326589524e7e606626) (thanks @wgaylord)
* [Add 24w04a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/1c4cacac6874d513e8725833aa53f0772b0e12b7) (thanks @github-actions[bot])
* [Add 24w03b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/****************************************) (thanks @github-actions[bot])
* [Add 24w03a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/edddc84ce25fb34c6c92aa995adf158272915a9e) (thanks @github-actions[bot])

## 3.59.3
* [pc: add `chatPacketsUseNbtComponents` feature for 1.20.3](https://github.com/PrismarineJS/minecraft-data/commit/42d7ecc1aa70dd0d7683aada09aea8c498d1a1a5) (thanks @extremeheat)

## 3.59.2
* [Fix pc dimensionDataIsAvailable feature range](https://github.com/PrismarineJS/minecraft-data/commit/afd576f9b3cbedc4c2f7cbca1e849dc3760027b1) (thanks @extremeheat)

## 3.59.1
* [Add pc feature `unifiedPlayerAndEntitySpawnPacket`](https://github.com/PrismarineJS/minecraft-data/commit/bd3c9757d0f9d40c797024ed09c3a5e99e815b77) (thanks @extremeheat)
* [Add an unicity test for features. (#824)](https://github.com/PrismarineJS/minecraft-data/commit/587a1341c83ef4fc1cf3353d35b09435a11f8217) (thanks @rom1504)

## 3.59.0
* [Fix breeze and wind charge being type player. (#826)](https://github.com/PrismarineJS/minecraft-data/commit/1792f0d8035aa275702b6080d4176ca14a3f578a) (thanks @wgaylord)

## 3.58.0
* [Update flying-squid PC features (#823)](https://github.com/PrismarineJS/minecraft-data/commit/344619e053614d6ab5c97bf6477c99699ad57a46) (thanks @Pandapip1)

## 3.57.0
* [Add loginPacket for 1.20 / 1.20.1 (#821)](https://github.com/PrismarineJS/minecraft-data/commit/c508b6f7eec91724a830a71d663f90a141155330) (thanks @wgaylord)

## 3.56.0
* [Fix 1.20.2 and 1.20.3 metadata keys (#818)](https://github.com/PrismarineJS/minecraft-data/commit/7c65e2640ef7efad691ee9cbd22377c655e44c40) (thanks @wgaylord)
* [node 18 (#709)](https://github.com/PrismarineJS/minecraft-data/commit/4360b8461f655b943271d8101660dfb544c477f9) (thanks @rom1504)

## 3.55.0
* [Add missing seed field in 1.19 based on minecraft packet tests](https://github.com/PrismarineJS/minecraft-data/commit/5f9995a821311c9ac926a208f957bd76a468fe55) (thanks @rom1504)

## 3.54.0
* [1.20.3-1.20.4 protocol (#806)](https://github.com/PrismarineJS/minecraft-data/commit/1aabc9c5fe190b41d12b08e10ff025c32b8107ed) (thanks @wgaylord)

## 3.53.0
* [1.20.2 Protocol fixes (#812)](https://github.com/PrismarineJS/minecraft-data/commit/d4c6ab37e8257e0e74875ac8920e43377911ffe6) (thanks @wgaylord)

## 3.52.0
* [1.20.2: use new anonOptionalNbt and anonymousNbt types from prismarine-nbt, add loginPacket (#810)](https://github.com/PrismarineJS/minecraft-data/commit/15f860a55e2ea4c8a62c0eeeaaf05e30f9efb1ac) (thanks @extremeheat)

## 3.51.0
* [Fix bedrock recipe text encoding issue (#808)](https://github.com/PrismarineJS/minecraft-data/commit/900ebf212eb2b97fdbca3aa8527d2ec530a90f2b) (thanks @extremeheat)
* [1.20.2 protocol: nbt data can have different root data types (#807)](https://github.com/PrismarineJS/minecraft-data/commit/37e288067964e403c29d1d2f72a4acc9a5b7ba52) (thanks @extremeheat)
* [Add 23w51b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/f851a09e3042247d42ab4f2775f986d39b45b39e) (thanks @github-actions[bot])
* [Add 1.20.3 and 1.20.4 extract-able data (#803)](https://github.com/PrismarineJS/minecraft-data/commit/b1088724d565819f1f1c8ae4931ca3b2fe609b3a) (thanks @wgaylord)
* [Improve correctness of 1.8 item varieties data (#772)](https://github.com/PrismarineJS/minecraft-data/commit/cf23a7fa2561cbbbbddaf4adc63449d835ac8f74) (thanks @kaduvert)
* [Fix 1.20.2 Recipes and add test to check for flipped door Fixed #804 (#805)](https://github.com/PrismarineJS/minecraft-data/commit/0ee510f07de8c72efd3634494ab850c9b550c8be) (thanks @wgaylord)
* [Add 1.20.4 to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/a63198b3cecdd5397949c49aa123484cce77ceb5) (thanks @github-actions[bot])

## 3.50.0
* [Add bedrock 1.20.50 protocol data (#800)](https://github.com/PrismarineJS/minecraft-data/commit/3dd855a3c0e372daaa210fc698b8040dd693cae8) (thanks @extremeheat)

## 3.49.1
* Fix pc protocol web doc by removing stable-json-stringify
* [Fixed inconsistency with 'unsignedChatContent' in 'player_chat' packet (#795)](https://github.com/PrismarineJS/minecraft-data/commit/6fc1bb638fb04afefab4ddfa7e736f3727a23004) (thanks @Ynfuien)

## 3.49.0
* [Add hasConfigurationState to features.json (#790)](https://github.com/PrismarineJS/minecraft-data/commit/4d190951873034034dfe9d7c7fb3d0475e24c33b) (thanks @wgaylord)
* [Fix spelling issue and 1 packet field order change. (#793)](https://github.com/PrismarineJS/minecraft-data/commit/5a4d0a0834791b5b725923ac33c3532455539579) (thanks @wgaylord)
* [Add 23w46a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/fb58a7c3d068d281b9c4d18d2cd840410d0cd635) (thanks @github-actions[bot])
* [Add 23w45a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/cf8333fce886587364e6f510bca72a6e9d555da4) (thanks @github-actions[bot])

## 3.48.0
* [Add 1.20.2 to readme](https://github.com/PrismarineJS/minecraft-data/commit/c156c97cf249f0b5f27999b5282395156c177e37) (thanks @rom1504)
* [Add 1.20.2 data (#786)](https://github.com/PrismarineJS/minecraft-data/commit/e60570b5b567f0e802133f02bf2e4e3ea912dded) (thanks @wgaylord)
* [Add 23w44a to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/6921a493648f721b9b2ebb3684464f11645d10b5) (thanks @github-actions[bot])
* [Add 23w43b to pc protocolVersions.json](https://github.com/PrismarineJS/minecraft-data/commit/7b486716da6775f39bbaf755d090f135012b127b) (thanks @github-actions[bot])

## 3.47.0
* [Add bedrock 1.20.40 protocol data (#784)](https://github.com/PrismarineJS/minecraft-data/commit/0c1d4e649a4755c3226dd93174249e9a75fde12c) (thanks @CreeperG16)

## 3.46.2
* [Fix bedrock protocol CameraPresets packet(#782)](https://github.com/PrismarineJS/minecraft-data/commit/4139610a241411296a47723d6d978047c52cc7cf) (thanks @MrDiamond64)
* [Update Minecraft wiki references (#781)](https://github.com/PrismarineJS/minecraft-data/commit/16aaf99c002125749dbcbf165e3ead15e104f7e8) (thanks @Spongecade)
* [Add bedrock 1.20.15 data mapping (#778)](https://github.com/PrismarineJS/minecraft-data/commit/a2df4110718476dd9e6997f99ac419d2308c19bd) (thanks @irkmandeer)

## 3.46.1
* [Revert "Change has_stack_id in bedrock Item to a boolean" (#776)](https://github.com/PrismarineJS/minecraft-data/commit/f7d70f0ee654e3d4f59354734b340ff00f794355) (thanks @extremeheat)

## 3.46.0
* [Add bedrock 1.20.30 protocol data (#774)](https://github.com/PrismarineJS/minecraft-data/commit/dd5f730dd8b930cc1d6dc6a7f464a020064f160d) (thanks @extremeheat)
* [Update bedrock protocol data for has_stack_id in Item to a boolean (#770)](https://github.com/PrismarineJS/minecraft-data/commit/3928388ae32e44558cc342a888d95278a8a1ac70) (thanks @CreeperG16)
* Update protocolVersions.json

## 3.45.0
* [add features for prismarine chunk (#763)](https://github.com/PrismarineJS/minecraft-data/commit/1b9380edb159721bef2342d4214b0ebb3128c812) (thanks @extremeheat)
* [1.20 block data fixes (#766)](https://github.com/PrismarineJS/minecraft-data/commit/b130830580ea0cc5551cd736bdc139ac79d3eba7) (thanks @MathiasElgaard)
* [Handle snapshots in update checking workflow (#767)](https://github.com/PrismarineJS/minecraft-data/commit/b5037850d0f3abf77b2197535b042e23d012aad6) (thanks @extremeheat)

## 3.44.0
* [Elytra support (#761)](https://github.com/PrismarineJS/minecraft-data/commit/72a267757b8fbb8462f3c3f25001754af9d1f7c4) (thanks @lkwilson)
* [Bedrock stack and request network id fix for 1.16.220 to 1.20.10 (#762)](https://github.com/PrismarineJS/minecraft-data/commit/7f144de36862de3dfe16b8b6272fcbd286327e16) (thanks @irkmandeer)
* [1.19.4 protocol fixes (#759)](https://github.com/PrismarineJS/minecraft-data/commit/5c7e7960270ef329f7c9e037d08adbf5ae2ba3d6) (thanks @frej4189)

## 3.43.1
* [Fix bedrock update_soft_enum packet missing encoding (#758)](https://github.com/PrismarineJS/minecraft-data/commit/d58b99fec41b8c23820168228cca9e86d1f45165) (thanks @JSbETms)

## 3.43.0
* [Add legacy.json flattening mappings for bedrock blocks (#755)](https://github.com/PrismarineJS/minecraft-data/commit/60682082d2dda5d5e7827ae908236dc1fbd8b79d) (thanks @Flonja)

## 3.42.1
* [Sync itemDrop object types in blockLoot_schema and entityLoot_schema (#753)](https://github.com/PrismarineJS/minecraft-data/commit/9fc22eeab83e41cedb2fc1bbb225d4d3368280bc) (thanks @extremeheat)

## 3.42.0
* [Fix bedrock packet_education_settings field (#751)](https://github.com/PrismarineJS/minecraft-data/commit/25496b14e0176850f39ceadc134962e5f08922e2) (thanks @extremeheat)
* [Bedrock hotbar_slot type fix for 1.16.220 to 1.20.10 (#748)](https://github.com/PrismarineJS/minecraft-data/commit/1c5fb1f2517386b546c77b43e7f164943a338efa) (thanks @irkmandeer)
* [Bedrock 1.20.0 block, biome and item data (#747)](https://github.com/PrismarineJS/minecraft-data/commit/fecc3e9e55a03ee84195099cf821140e56b65844) (thanks @CreeperG16)
* [fix: correct schema types (#746)](https://github.com/PrismarineJS/minecraft-data/commit/7428c8522f23d6049d3246653fb2f366ae1faba9) (thanks @Eejit43)
* [Add bedrock language data (#743)](https://github.com/PrismarineJS/minecraft-data/commit/d34a2f4654f5429aa36bc6be23f2b0acc7dfb2a9) (thanks @CreeperG16)

## 3.41.0
* [Add explicitMaxDurability feature (#744)](https://github.com/PrismarineJS/minecraft-data/commit/e9bea106cda498671c11d62d2bb9d22accd7551a) (thanks @frej4189)
* [Update attributes schema, add bedrock data (#692)](https://github.com/PrismarineJS/minecraft-data/commit/fa4c64ea7c144bced0560b9b06c6eee2a25991f6) (thanks @extremeheat)
* [Fix values for enum type in available_commands packet (#742)](https://github.com/PrismarineJS/minecraft-data/commit/b04d0940925247e7aa45434094eb7b4b119322f1) (thanks @CreeperG16)
* [fix `command_node` (#740)](https://github.com/PrismarineJS/minecraft-data/commit/099a80ddad349bb15ff4c02407a233a555a8a83d) (thanks @Nickid2018)
* [Particle changes (#739)](https://github.com/PrismarineJS/minecraft-data/commit/787107dd4da145babde00d8708bb714f445d008f) (thanks @PondWader)
* [damage_event packet fix (#738)](https://github.com/PrismarineJS/minecraft-data/commit/37015f4fad97a9b2a91333b38bc22c4b6f5909e7) (thanks @PondWader)

## 3.40.0
* [Bedrock 1.20.10 protocol support (#734)](https://github.com/PrismarineJS/minecraft-data/commit/fa521866fb2c50af30d072413b2a12a5a2a5eb45) (thanks @CreeperG16)
* [Changes to 1.19.3, 1.19.4 and 1.20 protocols to fix packet_entity_sound_effect and packet_sound_effect to include optional soundEvent argument (#731)](https://github.com/PrismarineJS/minecraft-data/commit/8ab8476ae6da4a0d1f86c30bfc2120b7d417426b) (thanks @TheRedXD)
* [Bedrock protocol changes (#730)](https://github.com/PrismarineJS/minecraft-data/commit/f00c2ccc5a0763bc04db48705772df58158cf0dd) (thanks @irkmandeer)
* [Fixed typo in dataPaths.json for bedrock 1.19.21 (#726)](https://github.com/PrismarineJS/minecraft-data/commit/8f1ba18c79b4e1adffa4079739cb3663bdeca792) (thanks @irkmandeer)

## 3.39.0
* [Add commands workflow (#727)](https://github.com/PrismarineJS/minecraft-data/commit/ecc33128220bdb20dc813b484d5374a9bdd26d40) (thanks @extremeheat)
* [bedrock: Fix chunk request packet field in protocol (#725)](https://github.com/PrismarineJS/minecraft-data/commit/57e5888d8876a16a6011757065a1e7494ac25483) (thanks @irkmandeer)

## 3.38.0
* pc: Add "multiSidedSigns" feature + some missing data (@PondWader)

## 3.37.0
* pc: support 1.20.0 and 1.20.1 (thanks @PondWader)

## 3.36.1
* bedrock: Fixes to protocol data (thanks @MrDiamond64)

## 3.36.0
* pc: add entity metadata (@extremeheat)

## 3.35.0
* Add bedrock 1.20.0 protocol data

## 3.34.0
* Partially updated protocol to 1.19.4 (thanks @extremeheat @TFBosoN @frej4189 @bradisson)

## 3.33.0
* Use node 18.

## 3.32.0
* Add bedrock 1.19.80 protocol data

## 3.31.0
* Add bedrock 1.19.70 protocol data (@CreeperG16)
* Fix bedrock protocol item CanPlace/CanDestroy fields (#694)

## 3.30.0
* Add enchanting data for bedrock versions

## 3.29.0
* Add bedrock 1.19.63 protocol (@stevarino)

## 3.28.0
* Add bedrock 1.19.62 protocol data (@CreeperG16)
* Update packet_login_plugin_response in pc 1.19.3 (@frej4189)

## 3.27.0
* Add bedrock 1.19.60 protocol data (@CreeperG16)

## 3.26.0
* 1.19.3 fixes
* Update protocolVersions.json with 1.19.3 snapshots

## 3.25.2
* Rename chat_command field in 1.19.2 (#671)

## 3.25.1

* Add 1.19.3 to protocolVersions (@frej4189)

## 3.25.0

* 1.19.3 fixed (@frej4189)

## 3.24.0

* 1.19.3 protocol (@frej4189)

## 3.23.0

* Add clientsideChatFormatting (@frej4189)

## 3.22.0

* 1.19.3 support (@frej4189)
* Add seed to entity-sound-effect (@u9g)
* Add 1.19 block mappings with new schema (@extremeheat)

## 3.21.0

* 1.19.1 fixes  (@extremeheat)

## 3.20.0
* bedrock : add 1.19.50 protocol data (@WillQizza)

## 3.19.0
* fix 1.19.2 protocol
* add all 1.19.2 data (@u9g)

## 3.18.0
* support 1.19.2 (@jtsiskin)

## 3.17.0
* bedrock : add 1.19.40 protocol data

## 3.16.0
* Moving features to mcData (@Epirito)

## 3.15.3
* Fix pc1.19 villager trade packet (#641) 

## 3.15.1
* Fix pc1.19 entity spawn packet (#639)

## 3.15.0

* added Burger extracted recipes.json for 1.19 (thanks @FCKJohni)

## 3.14.0

* update 1.19 features list (@extremeheat)
* Add features.json for bedrock (@extremeheat)

## 3.13.0

* bedrock : add protocol data for 1.19.30 (@stevarino)
* pc : Fix login packet structure in 1.19 (#632) @mkorman9

## 3.12.0

* pc: fix protocol login packet and update feature.json for 1.19 (@extremeheat)

## 3.11.0

* bedrock: add protocol data for 1.19.21 (@stevarino)

## 3.10.2

* bedrock: fix 1.19.20 map data packet

## 3.10.1

* node-minecraft-data release

## 3.10.0

* bedrock: add protocol data for 1.19.20 (@stevarino)

## 3.9.1

* pc: fix packet_respawn (@rob9315)

## 3.9.0

* bedrock: Add bedrock 1.19 data (#600)
* bedrock: protocol corrections
* pc: Add 1.19.1-rc2 to protocol versions

## 3.8.0

* [1.19] update features and add some @rob9315
* replace varint with custom native varlong @rob9315

## 3.7.3

* Move array back to declare_commands @rob9315

## 3.7.2

* Fix packet declare command @rob9315

## 3.7.1

* bedrock: 1.19 fixes @extremeheat
* pc: 1.19 fixes @rob9315

## 3.7.0

* pc: 1.8-1.18 protocol fixes from @rob9315
* pc: 1.19 protocol from @U9G

## 3.6.0
* bedrock: Add bedrock 1.19.10 protocol data (#588) @stevarino
* pc: Fix 1.18 language.json (#585) @Eejit43
* pc: Add 1.19.1-pre4, 1.19.1-pre3 to protocol versions 

## 3.5.1
* Fix Github pages docs

## 3.5.0
* pc: Add 1.19 Data (all but protocol) @u9g
* pc: Add blockloot for 1.18 and 1.19 @Eejit43

## 3.4.0
* bedrock: Add bedrock 1.19 protocol data, 1.18 protocol corrections

## 3.3.0
* pc: Update dimension descriptions (#565)
* pc: Add 1.19-pre4 to pc/common/protocolVersions.json
* bedrock: Add bedrock 1.18.30 block, item, recipe data
* bedrock: 1.18 protocol fixes
* pc: Add 1.19 to common/protocolVersions.json

## 3.2.0
* pc: Fix 1.17+ biome colors (@Karang)
* pc: Fix shulker box and ender pearl stack sizes (@RoseChilds)
* pc: Fixed hardness & resistance of copper_ore (@sefirosweb)
* pc: Fix guitar spelling in instruments.json (@MrAwesome)
* bedrock: Add bedrock 1.18 block/entity loot data, recipe data fix (@extremeheat)

## 3.1.1
* Fix Github CI release action

## 3.1.0
* bedrock: Add 1.18.30 protocol data

## 3.0.0
* nmd: remove findItemOrBlockById and findItemOrBlockByName entirely

## 2.221.0
* bedrock: Add bedrock 1.18.11 data
* pc: Fix to 1.18.2 login packet

## 2.220.0
* pc: Fix effect names in 1.17

## 2.119.0
* reverse bedrock version list

## 2.118.0
* Add prismarine-item features

## 2.117.0
* Add "metadataIxOfItem" feature

## 2.116.0
* pc: Fix loginPacket for 1.18.2

## 2.115.1
* pc: Fix name conflict with existing "version" and type from features.json

## 2.115.0
* pc: Add features.json from mineflayer

## 2.114.1
* pc: fix 1.18.2 data paths

## 2.114.0
* mcpc 1.18.2 protocol support

## 2.113.3
* bedrock: correction to protocol data for subchunk packets without caching for 1.18.11

## 2.113.2
* new release because 2.113.1 of nmd already exists

## 2.113.1
* bedrock: Corrections to protocol data for subchunk packets for 1.18.11

## 2.113.0
* bedrock: add 1.18.11 protocol data

## 2.112.0
* bedrock: version data parity, block state and protocol fixes

## 2.111.0
* pc: add missing attributes pointer for 1.18

## 2.110.0
* pc: add cake outshapes (@nickelpro, @SaubereSache)

## 2.109.0
* pc: fix version for 1.18.1

## 2.108.0
* pc: support 1.18.1

## 2.107.0
* pc: y is signed in 1.18 packet_multi_block_change

## 2.106.0
* pc: add materials 1.18

## 2.105.0
* bedrock: Add block data for 1.16.220, 1.17.0 (@extremeheat)

## 2.104.0
* pc: fix biomes for 1.18

## 2.103.0
* pc: biomes for 1.18

## 2.102.0
* pc: loginPacket for 1.18

## 2.101.0
* pc: fix states in blocks 1.18

## 2.100.0
* pc: add more 1.18 data
* bedrock: update protocol
* pc: add attribute support

## 2.99.3
* pc: rename x_sec and z_sec to x and z in chunk block entity

## 2.99.2
* pc: change nbtData field of chunkBlockEntity to optionalNbt in 1.18

## 2.99.1
* pc: fix simulation distance packet in 1.18

## 2.99.0
* pc: fix version path in 1.18 (@u9g)
* bedrock: update 1.18 data (@extremeheat)

## 2.98.1
* pc: Properly prefix particle resourceIDs in 1.17+ (@nickelpro)

## 2.98.0
* pc: Add 1.18 protocol data (@nickelpro)

## 2.97.0
* bedrock: Add 1.18.0 data (@extremeheat)

## 2.96.0
* Add bedrock 1.17.40 protocol data (@extremeheat)

## 2.95.0
* bedrock: Add 1.17.30 protocol data

## 2.94.0
* bedrock: Add skin data paths for older versions

## 2.93.0
* Fix enchantment.json saying that thorns goes on chestplate (@u9g)

## 2.92.0
* protocol packet field type mismatch fix (@Gnog3)
* Add bedrock protocol data (@extremeheat)
* remap u32 to varint in protocol.json (@kvoli)
* bedrock: remove minecraft: prefix from blockstates (@extremeheat)

## 2.91.0
* Rename `clicked_item` to `cursorItem` in 1.17+ window_click packet (@nickelpro)

## 2.90.0
* Add 1.17 recipes (@nickelpro)
* Rename 1.17.1 entity_destroy (@u9g)
* 1.7 protocol fix (thanks @Beaness)

## 2.89.2
* fixes to bedrock paths

## 2.89.1
* fix some remaining occurences of pe

## 2.89.0
* Relocate smelting_format and tags to types map (@nickelpro)
* Rework and extract all items.json (@nickelpro)
* Add bedrock edition data (@extremeheat)

## 2.88.0
* change primaryBitMask to bitMap in 1.17.x protocol.jsons (@Karang)

## 2.87.0
* add 1.17.1 protocol support (@u9g)
* add promptMessage arg to 1.17 (@u9g)

## 2.86.0
* add 1.17 login packet (@u9g)

## 2.85.3
* fix warningBlocks

## 2.85.2
* Remove additional s in 1.17 protocol
* Brigadier fix (@nickelpro)

## 2.85.1
* small 1.17 protocol fix (@nickelpro)

## 2.85.0
* 1.17.0 support (@Archengius and @nickelpro)
* Add netherite tools to harvestTools (@timoreo22)
* fix recipe shape (x/z flipped) in 1.16.2 protocol declare_recipes (@Gjum)

## 2.84.0
* Add legacy.json 1.12 -> 1.13 mappings

## 2.83.1
* Fix 21w07a protocol (@U9G)

## 2.83.0
* Fix biomes (@IceTank)

## 2.82.2
* fix 21w07a protocol (@U9G)

## 2.82.1
* fix datapath

## 2.82.0
* 21w07a protocol (@U9G)
* Fixed spawn painting for 1.7 (@SiebeDW)

## 2.81.0
* Add blast resistance (thanks @Moondarker)

## 2.80.0
* Add tints.json (thanks @Moondarker)

## 2.79.0
* Add properties to items.json (maxDurability, fixedWith, enchantCategories)

## 2.78.0
* Finish propogating all new properties to old enchantment.json files & remove enchant multipliers

## 2.77.0
* Propogate enchant categories & max levels of enchants to all old enchantments.json files

## 2.76.0
* Add enchantment multipliers

## 2.75.0
* Fix 1.16 collision shapes

## 2.74.0
* Add more biome data

## 2.73.1
* Add 1.16.5 to datapaths

## 2.73.0
* Add 1.16.4 and 1.13 enchantments data

## 2.72.0
* Fix upside-down recipes in 1.11-1.16 and improve recipe audit (thanks @Karang)

## 2.71.0
* Added map icons (@FalcoG)

## 2.70.1
* fix 1.16.2 block.json file (thanks @DeltaEvo)

## 2.70.0
* 1.16.4 support

## 2.69.0
* add example login packet (required in new versions) (thanks @GroobleDierne)

## 2.68.1
* add command to datapath file

## 2.68.0
* 1.16.3 support (same as 1.16.2)
* command entries

## 2.67.0
* use stack size range property in loots
* fix enchantments in recent versions

## 2.66.0
* add loottable information (thanks @TheDudeFromCI)
* add more 1.16.2 data (thanks @DrakoTrogdor)

## 2.65.0
* fix particle type (thanks @nickelpro)
* 1.16.2 (thanks @nickelpro)

## 2.64.0
* add particles (thanks @nickelpro)

## 2.63.0
* update instruments.json from 1.13 onward (thanks @Naomi)
* add correct drops for 1.13 onward (thanks @TheDudeFromCI)
* various protocol fix (thanks @nickelpro)

## 2.62.1
* fix items 1.16.1

## 2.62.0
* add foods data (thanks @AppDevMichael)

## 2.61.0
* extract proper states + default state from minecraft generator (thanks @Karang)

## 2.60.0
* full 1.16 support (thanks @AppDevMichael)

## 2.59.0
* 1.16.1 protocol support (thanks @Health123)

## 2.58.0
* 1.16 support

## 2.57.0
* fix abilities and recipes packets for 1.16-rc1

## 2.56.0
* add 1.16-rc1 support

## 2.55.0
* entity metadata type is a varint since 1.13

## 2.54.0
* complete items.json files all version (thanks @Karang)

## 2.53.0
* point to other version files for 1.15, 1.15.1, 1.14 and 1.14.1

## 2.52.0
* fix and add block shapes for more versions (thanks @Karang)

## 2.51.0
* more 1.15.2 data (thanks @uncovery)

## 2.50.0
* fix for elyctra (thanks @Mstrodl)
* more 1.14.4 data

## 2.49.0
* fix 1.14.4 blocks (and tests)

## 2.48.0
* fix bounding boxes (@Karang)
* fix some categories (@ImHarvol)

## 2.47.0
* add biomes, blocks, entities, items and recipes for 1.14.4

## 2.46.0
* fix entities for 1.13

## 2.45.0
* fix grass bounding box for 1.13
* last 1.16 snapshots support

## 2.44.0
* small fix to success packet for 20w13b

## 2.43.0
* Provide block collision shapes (thanks @Gjum)
* support snapshot 20w13b of 1.16 (thanks @sbh1311)

## 2.42.0
* Fix mushrooms' bounding boxes (thanks @IdanHo)
* 1.15.2 protocol support

## 2.41.0
* 1.15 protocol support

## 2.40.0
* 1.15.1 protocol support
* various data corrections for blocks (thanks @kemesa7)
* fix stack sizes (thanks @timmyRS)
* add item durability (thanks @timmyRS)

## 2.39.0
* 1.14.4 support

## 2.38.0
* 1.14.3 support

## 2.37.5
* fix intfield -> objectData in spawn_entity in all versions > 1.8

## 2.37.4
* add protocol to 1.14

## 2.37.3
* fix stonecutting in declare_recipes 1.14.1 : only one ingredient

## 2.37.2
* u32 -> i32 in 1.14

## 2.37.1
* add missing version file in 1.14.1 and 1.14

## 2.37.0
* fix redstone
* fix some block properties
* 1.14 support : protocol.json and some of the data : not everything is there yet

## 2.36.0
* fix team prefix and suffix in 1.13

## 2.35.0
* add block state data for 1.13 and 1.13.2

## 2.34.0
* support 1.13.2-pre2 and 1.13.2

## 2.33.0
* fix version definition for 1.13.2-pre1

## 2.32.0
* support 1.13.2-pre1

## 2.31.0
* fix 1.13.1 datapath

## 2.30.0
* update ajv, mocha and standard

## 2.29.0
* full 1.13 and 1.13.1 support (thanks @lluiscab for doing this)

## 2.28.0
* support of 1.13.1 protocol

## 2.27.0
* support of 1.13 protocol

## 2.26.0
* move js tests to standard

## 2.25.0
* fix packet_title starting from 1.11 (see http://wiki.vg/index.php?title=Protocol&oldid=8543#Title)

## 2.24.0
* fix brigadier:string parser properties

## 2.23.0
* some fixes for 17w50a protocol

## 2.22.0
* mcpc 17w50a support (first supported 1.13 snapshot)

## 2.21.0
* mcpc 1.12.2 support

## 2.20.0
* mcpc 1.12.1 support

## 2.19.0
* add language data

## 2.18.0
* mcpc 1.12 : add all the data (in addition to protocol)

## 2.17.0
* mcpc 1.12 support

## 2.16.0
* supports 1.12-pre4

## 2.15.0
* supports 17w18b

## 2.14.0
* supports 17w15a

## 2.13.2
* correct file names

## 2.13.1

* fix id for custom_payload in 1.11.2

## 2.13.0

* protocol_comments -> protocolComments

## 2.12.0

* add protocol comments

## 2.11.0

* add dataPaths file

## 2.10.0

* complete 1.11 data

## 2.9.0

* mcpc 1.11.2 support

## 2.8.0

* mcpe 1.0 support (except the protocol)

## 2.7.0

* 1.11 support (only the protocol)

## 2.6.0

* add classic blocks (thanks @mhsjlw)

## 2.5.0

* add 16w35a
* add enchantments data

## 2.4.0

* fix spelling error in protocol.json (catagory)
* add mcpe 0.15 protocol, blocks and items and update mcpe versions file
* add mcpc 1.10.1 and 1.10.2 and update mcpc versions file

## 2.3.1

* fix 1.10 version

## 2.3.0

* add 1.10 data

## 2.2.0

 * add license
 * add pe protocol

## 2.1.0

 * add 1.10-pre1

## 2.0.0

 * fix minecraftVersion in 16w20a
 * add a regex to validate the version strings
 * add pe blocks.json and items.json
 * BREAKING : move all pc data to pc dir

## 1.1.0

 * add 1.10 support (16w20a)

## 1.0.0

 * lot of minecraft version added
 * improve entities.json
 * add windows.json
 * other improvements : see commits log

## 1.8-0.1.0
 * first version after the versions split
 * move js files to tools/js
 * use countType in protocol.json

## 0.4.0
 * add some basic (to be used for manual updating) protocol extractors
 * import protocol.json from node-minecraft-protocol for version 1.8 of minecraft

## 0.3.0
 * remove id indexing from biomes, blocks, entities, items and instruments : let users (for examples node-minecraft-data) provide their indexing (by id, name,...)

## 0.2.1
 * entities is now in the API

## 0.2.0
 * update blocks, entities, items and recipes enums with new wiki extractors
 * add entities displayName
 * add drops in blocks
 * add metadata variations in blocks and drops
 * update recipes with variations of blocks and items
 * amount -> count and meta -> metadata in recipes
 * reorganize and improve wiki extractors

## 0.1.1
 * some new wiki extractors : beginning of work for blocks, entities
 * fix some recipes
 * add entities.json file

## 0.1.0
 * add json schemas to check the enums schemas
 * use circle ci the check the enums schemas automatically
 * add docson documentation for the schemas
 * change the format of recipes
 * add doc/recipes.md

## 0.0.1

 * first version
 * enums in enums/
 * scripts to audit and generate the enums in bin/
 * support minecraft 1.8 with some missing data
