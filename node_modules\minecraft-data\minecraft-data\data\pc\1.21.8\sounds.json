[{"id": 1, "name": "entity.allay.ambient_with_item"}, {"id": 2, "name": "entity.allay.ambient_without_item"}, {"id": 3, "name": "entity.allay.death"}, {"id": 4, "name": "entity.allay.hurt"}, {"id": 5, "name": "entity.allay.item_given"}, {"id": 6, "name": "entity.allay.item_taken"}, {"id": 7, "name": "entity.allay.item_thrown"}, {"id": 8, "name": "ambient.cave"}, {"id": 9, "name": "ambient.basalt_deltas.additions"}, {"id": 10, "name": "ambient.basalt_deltas.loop"}, {"id": 11, "name": "ambient.basalt_deltas.mood"}, {"id": 12, "name": "ambient.crimson_forest.additions"}, {"id": 13, "name": "ambient.crimson_forest.loop"}, {"id": 14, "name": "ambient.crimson_forest.mood"}, {"id": 15, "name": "ambient.nether_wastes.additions"}, {"id": 16, "name": "ambient.nether_wastes.loop"}, {"id": 17, "name": "ambient.nether_wastes.mood"}, {"id": 18, "name": "ambient.soul_sand_valley.additions"}, {"id": 19, "name": "ambient.soul_sand_valley.loop"}, {"id": 20, "name": "ambient.soul_sand_valley.mood"}, {"id": 21, "name": "ambient.warped_forest.additions"}, {"id": 22, "name": "ambient.warped_forest.loop"}, {"id": 23, "name": "ambient.warped_forest.mood"}, {"id": 24, "name": "ambient.underwater.enter"}, {"id": 25, "name": "ambient.underwater.exit"}, {"id": 26, "name": "ambient.underwater.loop"}, {"id": 27, "name": "ambient.underwater.loop.additions"}, {"id": 28, "name": "ambient.underwater.loop.additions.rare"}, {"id": 29, "name": "ambient.underwater.loop.additions.ultra_rare"}, {"id": 30, "name": "block.amethyst_block.break"}, {"id": 31, "name": "block.amethyst_block.chime"}, {"id": 32, "name": "block.amethyst_block.fall"}, {"id": 33, "name": "block.amethyst_block.hit"}, {"id": 34, "name": "block.amethyst_block.place"}, {"id": 35, "name": "block.amethyst_block.resonate"}, {"id": 36, "name": "block.amethyst_block.step"}, {"id": 37, "name": "block.amethyst_cluster.break"}, {"id": 38, "name": "block.amethyst_cluster.fall"}, {"id": 39, "name": "block.amethyst_cluster.hit"}, {"id": 40, "name": "block.amethyst_cluster.place"}, {"id": 41, "name": "block.amethyst_cluster.step"}, {"id": 42, "name": "block.ancient_debris.break"}, {"id": 43, "name": "block.ancient_debris.step"}, {"id": 44, "name": "block.ancient_debris.place"}, {"id": 45, "name": "block.ancient_debris.hit"}, {"id": 46, "name": "block.ancient_debris.fall"}, {"id": 47, "name": "block.anvil.break"}, {"id": 48, "name": "block.anvil.destroy"}, {"id": 49, "name": "block.anvil.fall"}, {"id": 50, "name": "block.anvil.hit"}, {"id": 51, "name": "block.anvil.land"}, {"id": 52, "name": "block.anvil.place"}, {"id": 53, "name": "block.anvil.step"}, {"id": 54, "name": "block.anvil.use"}, {"id": 55, "name": "entity.armadillo.eat"}, {"id": 56, "name": "entity.armadillo.hurt"}, {"id": 57, "name": "entity.armadillo.hurt_reduced"}, {"id": 58, "name": "entity.armadillo.ambient"}, {"id": 59, "name": "entity.armadillo.step"}, {"id": 60, "name": "entity.armadillo.death"}, {"id": 61, "name": "entity.armadillo.roll"}, {"id": 62, "name": "entity.armadillo.land"}, {"id": 63, "name": "entity.armadillo.scute_drop"}, {"id": 64, "name": "entity.armadillo.unroll_finish"}, {"id": 65, "name": "entity.armadillo.peek"}, {"id": 66, "name": "entity.armadillo.unroll_start"}, {"id": 67, "name": "entity.armadillo.brush"}, {"id": 68, "name": "item.armor.equip_chain"}, {"id": 69, "name": "item.armor.equip_diamond"}, {"id": 70, "name": "item.armor.equip_elytra"}, {"id": 71, "name": "item.armor.equip_generic"}, {"id": 72, "name": "item.armor.equip_gold"}, {"id": 73, "name": "item.armor.equip_iron"}, {"id": 74, "name": "item.armor.equip_leather"}, {"id": 75, "name": "item.armor.equip_netherite"}, {"id": 76, "name": "item.armor.equip_turtle"}, {"id": 77, "name": "item.armor.equip_wolf"}, {"id": 78, "name": "item.armor.unequip_wolf"}, {"id": 79, "name": "entity.armor_stand.break"}, {"id": 80, "name": "entity.armor_stand.fall"}, {"id": 81, "name": "entity.armor_stand.hit"}, {"id": 82, "name": "entity.armor_stand.place"}, {"id": 83, "name": "entity.arrow.hit"}, {"id": 84, "name": "entity.arrow.hit_player"}, {"id": 85, "name": "entity.arrow.shoot"}, {"id": 86, "name": "item.axe.strip"}, {"id": 87, "name": "item.axe.scrape"}, {"id": 88, "name": "item.axe.wax_off"}, {"id": 89, "name": "entity.axolotl.attack"}, {"id": 90, "name": "entity.axolotl.death"}, {"id": 91, "name": "entity.axolotl.hurt"}, {"id": 92, "name": "entity.axolotl.idle_air"}, {"id": 93, "name": "entity.axolotl.idle_water"}, {"id": 94, "name": "entity.axolotl.splash"}, {"id": 95, "name": "entity.axolotl.swim"}, {"id": 96, "name": "block.azalea.break"}, {"id": 97, "name": "block.azalea.fall"}, {"id": 98, "name": "block.azalea.hit"}, {"id": 99, "name": "block.azalea.place"}, {"id": 100, "name": "block.azalea.step"}, {"id": 101, "name": "block.azalea_leaves.break"}, {"id": 102, "name": "block.azalea_leaves.fall"}, {"id": 103, "name": "block.azalea_leaves.hit"}, {"id": 104, "name": "block.azalea_leaves.place"}, {"id": 105, "name": "block.azalea_leaves.step"}, {"id": 106, "name": "block.bamboo.break"}, {"id": 107, "name": "block.bamboo.fall"}, {"id": 108, "name": "block.bamboo.hit"}, {"id": 109, "name": "block.bamboo.place"}, {"id": 110, "name": "block.bamboo.step"}, {"id": 111, "name": "block.bamboo_sapling.break"}, {"id": 112, "name": "block.bamboo_sapling.hit"}, {"id": 113, "name": "block.bamboo_sapling.place"}, {"id": 114, "name": "block.bamboo_wood.break"}, {"id": 115, "name": "block.bamboo_wood.fall"}, {"id": 116, "name": "block.bamboo_wood.hit"}, {"id": 117, "name": "block.bamboo_wood.place"}, {"id": 118, "name": "block.bamboo_wood.step"}, {"id": 119, "name": "block.bamboo_wood_door.close"}, {"id": 120, "name": "block.bamboo_wood_door.open"}, {"id": 121, "name": "block.bamboo_wood_trapdoor.close"}, {"id": 122, "name": "block.bamboo_wood_trapdoor.open"}, {"id": 123, "name": "block.bamboo_wood_button.click_off"}, {"id": 124, "name": "block.bamboo_wood_button.click_on"}, {"id": 125, "name": "block.bamboo_wood_pressure_plate.click_off"}, {"id": 126, "name": "block.bamboo_wood_pressure_plate.click_on"}, {"id": 127, "name": "block.bamboo_wood_fence_gate.close"}, {"id": 128, "name": "block.bamboo_wood_fence_gate.open"}, {"id": 129, "name": "block.barrel.close"}, {"id": 130, "name": "block.barrel.open"}, {"id": 131, "name": "block.basalt.break"}, {"id": 132, "name": "block.basalt.step"}, {"id": 133, "name": "block.basalt.place"}, {"id": 134, "name": "block.basalt.hit"}, {"id": 135, "name": "block.basalt.fall"}, {"id": 136, "name": "entity.bat.ambient"}, {"id": 137, "name": "entity.bat.death"}, {"id": 138, "name": "entity.bat.hurt"}, {"id": 139, "name": "entity.bat.loop"}, {"id": 140, "name": "entity.bat.takeoff"}, {"id": 141, "name": "block.beacon.activate"}, {"id": 142, "name": "block.beacon.ambient"}, {"id": 143, "name": "block.beacon.deactivate"}, {"id": 144, "name": "block.beacon.power_select"}, {"id": 145, "name": "entity.bee.death"}, {"id": 146, "name": "entity.bee.hurt"}, {"id": 147, "name": "entity.bee.loop_aggressive"}, {"id": 148, "name": "entity.bee.loop"}, {"id": 149, "name": "entity.bee.sting"}, {"id": 150, "name": "entity.bee.pollinate"}, {"id": 151, "name": "block.beehive.drip"}, {"id": 152, "name": "block.beehive.enter"}, {"id": 153, "name": "block.beehive.exit"}, {"id": 154, "name": "block.beehive.shear"}, {"id": 155, "name": "block.beehive.work"}, {"id": 156, "name": "block.bell.use"}, {"id": 157, "name": "block.bell.resonate"}, {"id": 158, "name": "block.big_dripleaf.break"}, {"id": 159, "name": "block.big_dripleaf.fall"}, {"id": 160, "name": "block.big_dripleaf.hit"}, {"id": 161, "name": "block.big_dripleaf.place"}, {"id": 162, "name": "block.big_dripleaf.step"}, {"id": 163, "name": "entity.blaze.ambient"}, {"id": 164, "name": "entity.blaze.burn"}, {"id": 165, "name": "entity.blaze.death"}, {"id": 166, "name": "entity.blaze.hurt"}, {"id": 167, "name": "entity.blaze.shoot"}, {"id": 168, "name": "entity.boat.paddle_land"}, {"id": 169, "name": "entity.boat.paddle_water"}, {"id": 170, "name": "entity.bogged.ambient"}, {"id": 171, "name": "entity.bogged.death"}, {"id": 172, "name": "entity.bogged.hurt"}, {"id": 173, "name": "entity.bogged.shear"}, {"id": 174, "name": "entity.bogged.step"}, {"id": 175, "name": "block.bone_block.break"}, {"id": 176, "name": "block.bone_block.fall"}, {"id": 177, "name": "block.bone_block.hit"}, {"id": 178, "name": "block.bone_block.place"}, {"id": 179, "name": "block.bone_block.step"}, {"id": 180, "name": "item.bone_meal.use"}, {"id": 181, "name": "item.book.page_turn"}, {"id": 182, "name": "item.book.put"}, {"id": 183, "name": "block.blastfurnace.fire_crackle"}, {"id": 184, "name": "item.bottle.empty"}, {"id": 185, "name": "item.bottle.fill"}, {"id": 186, "name": "item.bottle.fill_dragonbreath"}, {"id": 187, "name": "entity.breeze.charge"}, {"id": 188, "name": "entity.breeze.deflect"}, {"id": 189, "name": "entity.breeze.inhale"}, {"id": 190, "name": "entity.breeze.idle_ground"}, {"id": 191, "name": "entity.breeze.idle_air"}, {"id": 192, "name": "entity.breeze.shoot"}, {"id": 193, "name": "entity.breeze.jump"}, {"id": 194, "name": "entity.breeze.land"}, {"id": 195, "name": "entity.breeze.slide"}, {"id": 196, "name": "entity.breeze.death"}, {"id": 197, "name": "entity.breeze.hurt"}, {"id": 198, "name": "entity.breeze.whirl"}, {"id": 199, "name": "entity.breeze.wind_burst"}, {"id": 200, "name": "block.brewing_stand.brew"}, {"id": 201, "name": "item.brush.brushing.generic"}, {"id": 202, "name": "item.brush.brushing.sand"}, {"id": 203, "name": "item.brush.brushing.gravel"}, {"id": 204, "name": "item.brush.brushing.sand.complete"}, {"id": 205, "name": "item.brush.brushing.gravel.complete"}, {"id": 206, "name": "block.bubble_column.bubble_pop"}, {"id": 207, "name": "block.bubble_column.upwards_ambient"}, {"id": 208, "name": "block.bubble_column.upwards_inside"}, {"id": 209, "name": "block.bubble_column.whirlpool_ambient"}, {"id": 210, "name": "block.bubble_column.whirlpool_inside"}, {"id": 211, "name": "ui.hud.bubble_pop"}, {"id": 212, "name": "item.bucket.empty"}, {"id": 213, "name": "item.bucket.empty_axolotl"}, {"id": 214, "name": "item.bucket.empty_fish"}, {"id": 215, "name": "item.bucket.empty_lava"}, {"id": 216, "name": "item.bucket.empty_powder_snow"}, {"id": 217, "name": "item.bucket.empty_tadpole"}, {"id": 218, "name": "item.bucket.fill"}, {"id": 219, "name": "item.bucket.fill_axolotl"}, {"id": 220, "name": "item.bucket.fill_fish"}, {"id": 221, "name": "item.bucket.fill_lava"}, {"id": 222, "name": "item.bucket.fill_powder_snow"}, {"id": 223, "name": "item.bucket.fill_tadpole"}, {"id": 224, "name": "item.bundle.drop_contents"}, {"id": 225, "name": "item.bundle.insert"}, {"id": 226, "name": "item.bundle.insert_fail"}, {"id": 227, "name": "item.bundle.remove_one"}, {"id": 228, "name": "block.cactus_flower.break"}, {"id": 229, "name": "block.cactus_flower.place"}, {"id": 230, "name": "block.cake.add_candle"}, {"id": 231, "name": "block.calcite.break"}, {"id": 232, "name": "block.calcite.step"}, {"id": 233, "name": "block.calcite.place"}, {"id": 234, "name": "block.calcite.hit"}, {"id": 235, "name": "block.calcite.fall"}, {"id": 236, "name": "entity.camel.ambient"}, {"id": 237, "name": "entity.camel.dash"}, {"id": 238, "name": "entity.camel.dash_ready"}, {"id": 239, "name": "entity.camel.death"}, {"id": 240, "name": "entity.camel.eat"}, {"id": 241, "name": "entity.camel.hurt"}, {"id": 242, "name": "entity.camel.saddle"}, {"id": 243, "name": "entity.camel.sit"}, {"id": 244, "name": "entity.camel.stand"}, {"id": 245, "name": "entity.camel.step"}, {"id": 246, "name": "entity.camel.step_sand"}, {"id": 247, "name": "block.campfire.crackle"}, {"id": 248, "name": "block.candle.ambient"}, {"id": 249, "name": "block.candle.break"}, {"id": 250, "name": "block.candle.extinguish"}, {"id": 251, "name": "block.candle.fall"}, {"id": 252, "name": "block.candle.hit"}, {"id": 253, "name": "block.candle.place"}, {"id": 254, "name": "block.candle.step"}, {"id": 255, "name": "entity.cat.ambient"}, {"id": 256, "name": "entity.cat.stray_ambient"}, {"id": 257, "name": "entity.cat.death"}, {"id": 258, "name": "entity.cat.eat"}, {"id": 259, "name": "entity.cat.hiss"}, {"id": 260, "name": "entity.cat.beg_for_food"}, {"id": 261, "name": "entity.cat.hurt"}, {"id": 262, "name": "entity.cat.purr"}, {"id": 263, "name": "entity.cat.purreow"}, {"id": 264, "name": "block.cave_vines.break"}, {"id": 265, "name": "block.cave_vines.fall"}, {"id": 266, "name": "block.cave_vines.hit"}, {"id": 267, "name": "block.cave_vines.place"}, {"id": 268, "name": "block.cave_vines.step"}, {"id": 269, "name": "block.cave_vines.pick_berries"}, {"id": 270, "name": "block.chain.break"}, {"id": 271, "name": "block.chain.fall"}, {"id": 272, "name": "block.chain.hit"}, {"id": 273, "name": "block.chain.place"}, {"id": 274, "name": "block.chain.step"}, {"id": 275, "name": "block.cherry_wood.break"}, {"id": 276, "name": "block.cherry_wood.fall"}, {"id": 277, "name": "block.cherry_wood.hit"}, {"id": 278, "name": "block.cherry_wood.place"}, {"id": 279, "name": "block.cherry_wood.step"}, {"id": 280, "name": "block.cherry_sapling.break"}, {"id": 281, "name": "block.cherry_sapling.fall"}, {"id": 282, "name": "block.cherry_sapling.hit"}, {"id": 283, "name": "block.cherry_sapling.place"}, {"id": 284, "name": "block.cherry_sapling.step"}, {"id": 285, "name": "block.cherry_leaves.break"}, {"id": 286, "name": "block.cherry_leaves.fall"}, {"id": 287, "name": "block.cherry_leaves.hit"}, {"id": 288, "name": "block.cherry_leaves.place"}, {"id": 289, "name": "block.cherry_leaves.step"}, {"id": 290, "name": "block.cherry_wood_hanging_sign.step"}, {"id": 291, "name": "block.cherry_wood_hanging_sign.break"}, {"id": 292, "name": "block.cherry_wood_hanging_sign.fall"}, {"id": 293, "name": "block.cherry_wood_hanging_sign.hit"}, {"id": 294, "name": "block.cherry_wood_hanging_sign.place"}, {"id": 295, "name": "block.cherry_wood_door.close"}, {"id": 296, "name": "block.cherry_wood_door.open"}, {"id": 297, "name": "block.cherry_wood_trapdoor.close"}, {"id": 298, "name": "block.cherry_wood_trapdoor.open"}, {"id": 299, "name": "block.cherry_wood_button.click_off"}, {"id": 300, "name": "block.cherry_wood_button.click_on"}, {"id": 301, "name": "block.cherry_wood_pressure_plate.click_off"}, {"id": 302, "name": "block.cherry_wood_pressure_plate.click_on"}, {"id": 303, "name": "block.cherry_wood_fence_gate.close"}, {"id": 304, "name": "block.cherry_wood_fence_gate.open"}, {"id": 305, "name": "block.chest.close"}, {"id": 306, "name": "block.chest.locked"}, {"id": 307, "name": "block.chest.open"}, {"id": 308, "name": "entity.chicken.ambient"}, {"id": 309, "name": "entity.chicken.death"}, {"id": 310, "name": "entity.chicken.egg"}, {"id": 311, "name": "entity.chicken.hurt"}, {"id": 312, "name": "entity.chicken.step"}, {"id": 313, "name": "block.chiseled_bookshelf.break"}, {"id": 314, "name": "block.chiseled_bookshelf.fall"}, {"id": 315, "name": "block.chiseled_bookshelf.hit"}, {"id": 316, "name": "block.chiseled_bookshelf.insert"}, {"id": 317, "name": "block.chiseled_bookshelf.insert.enchanted"}, {"id": 318, "name": "block.chiseled_bookshelf.step"}, {"id": 319, "name": "block.chiseled_bookshelf.pickup"}, {"id": 320, "name": "block.chiseled_bookshelf.pickup.enchanted"}, {"id": 321, "name": "block.chiseled_bookshelf.place"}, {"id": 322, "name": "block.chorus_flower.death"}, {"id": 323, "name": "block.chorus_flower.grow"}, {"id": 324, "name": "item.chorus_fruit.teleport"}, {"id": 325, "name": "block.cobweb.break"}, {"id": 326, "name": "block.cobweb.step"}, {"id": 327, "name": "block.cobweb.place"}, {"id": 328, "name": "block.cobweb.hit"}, {"id": 329, "name": "block.cobweb.fall"}, {"id": 330, "name": "entity.cod.ambient"}, {"id": 331, "name": "entity.cod.death"}, {"id": 332, "name": "entity.cod.flop"}, {"id": 333, "name": "entity.cod.hurt"}, {"id": 334, "name": "block.comparator.click"}, {"id": 335, "name": "block.composter.empty"}, {"id": 336, "name": "block.composter.fill"}, {"id": 337, "name": "block.composter.fill_success"}, {"id": 338, "name": "block.composter.ready"}, {"id": 339, "name": "block.conduit.activate"}, {"id": 340, "name": "block.conduit.ambient"}, {"id": 341, "name": "block.conduit.ambient.short"}, {"id": 342, "name": "block.conduit.attack.target"}, {"id": 343, "name": "block.conduit.deactivate"}, {"id": 344, "name": "block.copper_bulb.break"}, {"id": 345, "name": "block.copper_bulb.step"}, {"id": 346, "name": "block.copper_bulb.place"}, {"id": 347, "name": "block.copper_bulb.hit"}, {"id": 348, "name": "block.copper_bulb.fall"}, {"id": 349, "name": "block.copper_bulb.turn_on"}, {"id": 350, "name": "block.copper_bulb.turn_off"}, {"id": 351, "name": "block.copper.break"}, {"id": 352, "name": "block.copper.step"}, {"id": 353, "name": "block.copper.place"}, {"id": 354, "name": "block.copper.hit"}, {"id": 355, "name": "block.copper.fall"}, {"id": 356, "name": "block.copper_door.close"}, {"id": 357, "name": "block.copper_door.open"}, {"id": 358, "name": "block.copper_grate.break"}, {"id": 359, "name": "block.copper_grate.step"}, {"id": 360, "name": "block.copper_grate.place"}, {"id": 361, "name": "block.copper_grate.hit"}, {"id": 362, "name": "block.copper_grate.fall"}, {"id": 363, "name": "block.copper_trapdoor.close"}, {"id": 364, "name": "block.copper_trapdoor.open"}, {"id": 365, "name": "block.coral_block.break"}, {"id": 366, "name": "block.coral_block.fall"}, {"id": 367, "name": "block.coral_block.hit"}, {"id": 368, "name": "block.coral_block.place"}, {"id": 369, "name": "block.coral_block.step"}, {"id": 370, "name": "entity.cow.ambient"}, {"id": 371, "name": "entity.cow.death"}, {"id": 372, "name": "entity.cow.hurt"}, {"id": 373, "name": "entity.cow.milk"}, {"id": 374, "name": "entity.cow.step"}, {"id": 375, "name": "block.crafter.craft"}, {"id": 376, "name": "block.crafter.fail"}, {"id": 377, "name": "entity.creaking.ambient"}, {"id": 378, "name": "entity.creaking.activate"}, {"id": 379, "name": "entity.creaking.deactivate"}, {"id": 380, "name": "entity.creaking.attack"}, {"id": 381, "name": "entity.creaking.death"}, {"id": 382, "name": "entity.creaking.step"}, {"id": 383, "name": "entity.creaking.freeze"}, {"id": 384, "name": "entity.creaking.unfreeze"}, {"id": 385, "name": "entity.creaking.spawn"}, {"id": 386, "name": "entity.creaking.sway"}, {"id": 387, "name": "entity.creaking.twitch"}, {"id": 388, "name": "block.creaking_heart.break"}, {"id": 389, "name": "block.creaking_heart.fall"}, {"id": 390, "name": "block.creaking_heart.hit"}, {"id": 391, "name": "block.creaking_heart.hurt"}, {"id": 392, "name": "block.creaking_heart.place"}, {"id": 393, "name": "block.creaking_heart.step"}, {"id": 394, "name": "block.creaking_heart.idle"}, {"id": 395, "name": "block.creaking_heart.spawn"}, {"id": 396, "name": "entity.creeper.death"}, {"id": 397, "name": "entity.creeper.hurt"}, {"id": 398, "name": "entity.creeper.primed"}, {"id": 399, "name": "block.crop.break"}, {"id": 400, "name": "item.crop.plant"}, {"id": 401, "name": "item.crossbow.hit"}, {"id": 402, "name": "item.crossbow.loading_end"}, {"id": 403, "name": "item.crossbow.loading_middle"}, {"id": 404, "name": "item.crossbow.loading_start"}, {"id": 405, "name": "item.crossbow.quick_charge_1"}, {"id": 406, "name": "item.crossbow.quick_charge_2"}, {"id": 407, "name": "item.crossbow.quick_charge_3"}, {"id": 408, "name": "item.crossbow.shoot"}, {"id": 409, "name": "block.deadbush.idle"}, {"id": 410, "name": "block.decorated_pot.break"}, {"id": 411, "name": "block.decorated_pot.fall"}, {"id": 412, "name": "block.decorated_pot.hit"}, {"id": 413, "name": "block.decorated_pot.insert"}, {"id": 414, "name": "block.decorated_pot.insert_fail"}, {"id": 415, "name": "block.decorated_pot.step"}, {"id": 416, "name": "block.decorated_pot.place"}, {"id": 417, "name": "block.decorated_pot.shatter"}, {"id": 418, "name": "block.deepslate_bricks.break"}, {"id": 419, "name": "block.deepslate_bricks.fall"}, {"id": 420, "name": "block.deepslate_bricks.hit"}, {"id": 421, "name": "block.deepslate_bricks.place"}, {"id": 422, "name": "block.deepslate_bricks.step"}, {"id": 423, "name": "block.deepslate.break"}, {"id": 424, "name": "block.deepslate.fall"}, {"id": 425, "name": "block.deepslate.hit"}, {"id": 426, "name": "block.deepslate.place"}, {"id": 427, "name": "block.deepslate.step"}, {"id": 428, "name": "block.deepslate_tiles.break"}, {"id": 429, "name": "block.deepslate_tiles.fall"}, {"id": 430, "name": "block.deepslate_tiles.hit"}, {"id": 431, "name": "block.deepslate_tiles.place"}, {"id": 432, "name": "block.deepslate_tiles.step"}, {"id": 433, "name": "block.dispenser.dispense"}, {"id": 434, "name": "block.dispenser.fail"}, {"id": 435, "name": "block.dispenser.launch"}, {"id": 436, "name": "entity.dolphin.ambient"}, {"id": 437, "name": "entity.dolphin.ambient_water"}, {"id": 438, "name": "entity.dolphin.attack"}, {"id": 439, "name": "entity.dolphin.death"}, {"id": 440, "name": "entity.dolphin.eat"}, {"id": 441, "name": "entity.dolphin.hurt"}, {"id": 442, "name": "entity.dolphin.jump"}, {"id": 443, "name": "entity.dolphin.play"}, {"id": 444, "name": "entity.dolphin.splash"}, {"id": 445, "name": "entity.dolphin.swim"}, {"id": 446, "name": "entity.donkey.ambient"}, {"id": 447, "name": "entity.donkey.angry"}, {"id": 448, "name": "entity.donkey.chest"}, {"id": 449, "name": "entity.donkey.death"}, {"id": 450, "name": "entity.donkey.eat"}, {"id": 451, "name": "entity.donkey.hurt"}, {"id": 452, "name": "entity.donkey.jump"}, {"id": 453, "name": "block.dried_ghast.break"}, {"id": 454, "name": "block.dried_ghast.step"}, {"id": 455, "name": "block.dried_ghast.fall"}, {"id": 456, "name": "block.dried_ghast.ambient"}, {"id": 457, "name": "block.dried_ghast.ambient_water"}, {"id": 458, "name": "block.dried_ghast.place"}, {"id": 459, "name": "block.dried_ghast.place_in_water"}, {"id": 460, "name": "block.dried_ghast.transition"}, {"id": 461, "name": "block.dripstone_block.break"}, {"id": 462, "name": "block.dripstone_block.step"}, {"id": 463, "name": "block.dripstone_block.place"}, {"id": 464, "name": "block.dripstone_block.hit"}, {"id": 465, "name": "block.dripstone_block.fall"}, {"id": 466, "name": "block.dry_grass.ambient"}, {"id": 467, "name": "block.pointed_dripstone.break"}, {"id": 468, "name": "block.pointed_dripstone.step"}, {"id": 469, "name": "block.pointed_dripstone.place"}, {"id": 470, "name": "block.pointed_dripstone.hit"}, {"id": 471, "name": "block.pointed_dripstone.fall"}, {"id": 472, "name": "block.pointed_dripstone.land"}, {"id": 473, "name": "block.pointed_dripstone.drip_lava"}, {"id": 474, "name": "block.pointed_dripstone.drip_water"}, {"id": 475, "name": "block.pointed_dripstone.drip_lava_into_cauldron"}, {"id": 476, "name": "block.pointed_dripstone.drip_water_into_cauldron"}, {"id": 477, "name": "block.big_dripleaf.tilt_down"}, {"id": 478, "name": "block.big_dripleaf.tilt_up"}, {"id": 479, "name": "entity.drowned.ambient"}, {"id": 480, "name": "entity.drowned.ambient_water"}, {"id": 481, "name": "entity.drowned.death"}, {"id": 482, "name": "entity.drowned.death_water"}, {"id": 483, "name": "entity.drowned.hurt"}, {"id": 484, "name": "entity.drowned.hurt_water"}, {"id": 485, "name": "entity.drowned.shoot"}, {"id": 486, "name": "entity.drowned.step"}, {"id": 487, "name": "entity.drowned.swim"}, {"id": 488, "name": "item.dye.use"}, {"id": 489, "name": "entity.egg.throw"}, {"id": 490, "name": "entity.elder_guardian.ambient"}, {"id": 491, "name": "entity.elder_guardian.ambient_land"}, {"id": 492, "name": "entity.elder_guardian.curse"}, {"id": 493, "name": "entity.elder_guardian.death"}, {"id": 494, "name": "entity.elder_guardian.death_land"}, {"id": 495, "name": "entity.elder_guardian.flop"}, {"id": 496, "name": "entity.elder_guardian.hurt"}, {"id": 497, "name": "entity.elder_guardian.hurt_land"}, {"id": 498, "name": "item.elytra.flying"}, {"id": 499, "name": "block.enchantment_table.use"}, {"id": 500, "name": "block.ender_chest.close"}, {"id": 501, "name": "block.ender_chest.open"}, {"id": 502, "name": "entity.ender_dragon.ambient"}, {"id": 503, "name": "entity.ender_dragon.death"}, {"id": 504, "name": "entity.dragon_fireball.explode"}, {"id": 505, "name": "entity.ender_dragon.flap"}, {"id": 506, "name": "entity.ender_dragon.growl"}, {"id": 507, "name": "entity.ender_dragon.hurt"}, {"id": 508, "name": "entity.ender_dragon.shoot"}, {"id": 509, "name": "entity.ender_eye.death"}, {"id": 510, "name": "entity.ender_eye.launch"}, {"id": 511, "name": "entity.enderman.ambient"}, {"id": 512, "name": "entity.enderman.death"}, {"id": 513, "name": "entity.enderman.hurt"}, {"id": 514, "name": "entity.enderman.scream"}, {"id": 515, "name": "entity.enderman.stare"}, {"id": 516, "name": "entity.enderman.teleport"}, {"id": 517, "name": "entity.endermite.ambient"}, {"id": 518, "name": "entity.endermite.death"}, {"id": 519, "name": "entity.endermite.hurt"}, {"id": 520, "name": "entity.endermite.step"}, {"id": 521, "name": "entity.ender_pearl.throw"}, {"id": 522, "name": "block.end_gateway.spawn"}, {"id": 523, "name": "block.end_portal_frame.fill"}, {"id": 524, "name": "block.end_portal.spawn"}, {"id": 525, "name": "entity.evoker.ambient"}, {"id": 526, "name": "entity.evoker.cast_spell"}, {"id": 527, "name": "entity.evoker.celebrate"}, {"id": 528, "name": "entity.evoker.death"}, {"id": 529, "name": "entity.evoker_fangs.attack"}, {"id": 530, "name": "entity.evoker.hurt"}, {"id": 531, "name": "entity.evoker.prepare_attack"}, {"id": 532, "name": "entity.evoker.prepare_summon"}, {"id": 533, "name": "entity.evoker.prepare_wololo"}, {"id": 534, "name": "entity.experience_bottle.throw"}, {"id": 535, "name": "entity.experience_orb.pickup"}, {"id": 536, "name": "block.eyeblossom.open_long"}, {"id": 537, "name": "block.eyeblossom.open"}, {"id": 538, "name": "block.eyeblossom.close_long"}, {"id": 539, "name": "block.eyeblossom.close"}, {"id": 540, "name": "block.eyeblossom.idle"}, {"id": 541, "name": "block.fence_gate.close"}, {"id": 542, "name": "block.fence_gate.open"}, {"id": 543, "name": "item.firecharge.use"}, {"id": 544, "name": "block.firefly_bush.idle"}, {"id": 545, "name": "entity.firework_rocket.blast"}, {"id": 546, "name": "entity.firework_rocket.blast_far"}, {"id": 547, "name": "entity.firework_rocket.large_blast"}, {"id": 548, "name": "entity.firework_rocket.large_blast_far"}, {"id": 549, "name": "entity.firework_rocket.launch"}, {"id": 550, "name": "entity.firework_rocket.shoot"}, {"id": 551, "name": "entity.firework_rocket.twinkle"}, {"id": 552, "name": "entity.firework_rocket.twinkle_far"}, {"id": 553, "name": "block.fire.ambient"}, {"id": 554, "name": "block.fire.extinguish"}, {"id": 555, "name": "entity.fish.swim"}, {"id": 556, "name": "entity.fishing_bobber.retrieve"}, {"id": 557, "name": "entity.fishing_bobber.splash"}, {"id": 558, "name": "entity.fishing_bobber.throw"}, {"id": 559, "name": "item.flintandsteel.use"}, {"id": 560, "name": "block.flowering_azalea.break"}, {"id": 561, "name": "block.flowering_azalea.fall"}, {"id": 562, "name": "block.flowering_azalea.hit"}, {"id": 563, "name": "block.flowering_azalea.place"}, {"id": 564, "name": "block.flowering_azalea.step"}, {"id": 565, "name": "entity.fox.aggro"}, {"id": 566, "name": "entity.fox.ambient"}, {"id": 567, "name": "entity.fox.bite"}, {"id": 568, "name": "entity.fox.death"}, {"id": 569, "name": "entity.fox.eat"}, {"id": 570, "name": "entity.fox.hurt"}, {"id": 571, "name": "entity.fox.screech"}, {"id": 572, "name": "entity.fox.sleep"}, {"id": 573, "name": "entity.fox.sniff"}, {"id": 574, "name": "entity.fox.spit"}, {"id": 575, "name": "entity.fox.teleport"}, {"id": 576, "name": "block.suspicious_sand.break"}, {"id": 577, "name": "block.suspicious_sand.step"}, {"id": 578, "name": "block.suspicious_sand.place"}, {"id": 579, "name": "block.suspicious_sand.hit"}, {"id": 580, "name": "block.suspicious_sand.fall"}, {"id": 581, "name": "block.suspicious_gravel.break"}, {"id": 582, "name": "block.suspicious_gravel.step"}, {"id": 583, "name": "block.suspicious_gravel.place"}, {"id": 584, "name": "block.suspicious_gravel.hit"}, {"id": 585, "name": "block.suspicious_gravel.fall"}, {"id": 586, "name": "block.froglight.break"}, {"id": 587, "name": "block.froglight.fall"}, {"id": 588, "name": "block.froglight.hit"}, {"id": 589, "name": "block.froglight.place"}, {"id": 590, "name": "block.froglight.step"}, {"id": 591, "name": "block.frogspawn.step"}, {"id": 592, "name": "block.frogspawn.break"}, {"id": 593, "name": "block.frogspawn.fall"}, {"id": 594, "name": "block.frogspawn.hatch"}, {"id": 595, "name": "block.frogspawn.hit"}, {"id": 596, "name": "block.frogspawn.place"}, {"id": 597, "name": "entity.frog.ambient"}, {"id": 598, "name": "entity.frog.death"}, {"id": 599, "name": "entity.frog.eat"}, {"id": 600, "name": "entity.frog.hurt"}, {"id": 601, "name": "entity.frog.lay_spawn"}, {"id": 602, "name": "entity.frog.long_jump"}, {"id": 603, "name": "entity.frog.step"}, {"id": 604, "name": "entity.frog.tongue"}, {"id": 605, "name": "block.roots.break"}, {"id": 606, "name": "block.roots.step"}, {"id": 607, "name": "block.roots.place"}, {"id": 608, "name": "block.roots.hit"}, {"id": 609, "name": "block.roots.fall"}, {"id": 610, "name": "block.furnace.fire_crackle"}, {"id": 611, "name": "entity.generic.big_fall"}, {"id": 612, "name": "entity.generic.burn"}, {"id": 613, "name": "entity.generic.death"}, {"id": 614, "name": "entity.generic.drink"}, {"id": 615, "name": "entity.generic.eat"}, {"id": 616, "name": "entity.generic.explode"}, {"id": 617, "name": "entity.generic.extinguish_fire"}, {"id": 618, "name": "entity.generic.hurt"}, {"id": 619, "name": "entity.generic.small_fall"}, {"id": 620, "name": "entity.generic.splash"}, {"id": 621, "name": "entity.generic.swim"}, {"id": 622, "name": "entity.ghast.ambient"}, {"id": 623, "name": "entity.ghast.death"}, {"id": 624, "name": "entity.ghast.hurt"}, {"id": 625, "name": "entity.ghast.scream"}, {"id": 626, "name": "entity.ghast.shoot"}, {"id": 627, "name": "entity.ghast.warn"}, {"id": 628, "name": "entity.ghastling.ambient"}, {"id": 629, "name": "entity.ghastling.death"}, {"id": 630, "name": "entity.ghastling.hurt"}, {"id": 631, "name": "entity.ghastling.spawn"}, {"id": 632, "name": "block.gilded_blackstone.break"}, {"id": 633, "name": "block.gilded_blackstone.fall"}, {"id": 634, "name": "block.gilded_blackstone.hit"}, {"id": 635, "name": "block.gilded_blackstone.place"}, {"id": 636, "name": "block.gilded_blackstone.step"}, {"id": 637, "name": "block.glass.break"}, {"id": 638, "name": "block.glass.fall"}, {"id": 639, "name": "block.glass.hit"}, {"id": 640, "name": "block.glass.place"}, {"id": 641, "name": "block.glass.step"}, {"id": 642, "name": "item.glow_ink_sac.use"}, {"id": 643, "name": "entity.glow_item_frame.add_item"}, {"id": 644, "name": "entity.glow_item_frame.break"}, {"id": 645, "name": "entity.glow_item_frame.place"}, {"id": 646, "name": "entity.glow_item_frame.remove_item"}, {"id": 647, "name": "entity.glow_item_frame.rotate_item"}, {"id": 648, "name": "entity.glow_squid.ambient"}, {"id": 649, "name": "entity.glow_squid.death"}, {"id": 650, "name": "entity.glow_squid.hurt"}, {"id": 651, "name": "entity.glow_squid.squirt"}, {"id": 652, "name": "entity.goat.ambient"}, {"id": 653, "name": "entity.goat.death"}, {"id": 654, "name": "entity.goat.eat"}, {"id": 655, "name": "entity.goat.hurt"}, {"id": 656, "name": "entity.goat.long_jump"}, {"id": 657, "name": "entity.goat.milk"}, {"id": 658, "name": "entity.goat.prepare_ram"}, {"id": 659, "name": "entity.goat.ram_impact"}, {"id": 660, "name": "entity.goat.horn_break"}, {"id": 661, "name": "entity.goat.screaming.ambient"}, {"id": 662, "name": "entity.goat.screaming.death"}, {"id": 663, "name": "entity.goat.screaming.eat"}, {"id": 664, "name": "entity.goat.screaming.hurt"}, {"id": 665, "name": "entity.goat.screaming.long_jump"}, {"id": 666, "name": "entity.goat.screaming.milk"}, {"id": 667, "name": "entity.goat.screaming.prepare_ram"}, {"id": 668, "name": "entity.goat.screaming.ram_impact"}, {"id": 669, "name": "entity.goat.step"}, {"id": 670, "name": "block.grass.break"}, {"id": 671, "name": "block.grass.fall"}, {"id": 672, "name": "block.grass.hit"}, {"id": 673, "name": "block.grass.place"}, {"id": 674, "name": "block.grass.step"}, {"id": 675, "name": "block.gravel.break"}, {"id": 676, "name": "block.gravel.fall"}, {"id": 677, "name": "block.gravel.hit"}, {"id": 678, "name": "block.gravel.place"}, {"id": 679, "name": "block.gravel.step"}, {"id": 680, "name": "block.grindstone.use"}, {"id": 681, "name": "block.growing_plant.crop"}, {"id": 682, "name": "entity.guardian.ambient"}, {"id": 683, "name": "entity.guardian.ambient_land"}, {"id": 684, "name": "entity.guardian.attack"}, {"id": 685, "name": "entity.guardian.death"}, {"id": 686, "name": "entity.guardian.death_land"}, {"id": 687, "name": "entity.guardian.flop"}, {"id": 688, "name": "entity.guardian.hurt"}, {"id": 689, "name": "entity.guardian.hurt_land"}, {"id": 690, "name": "block.hanging_roots.break"}, {"id": 691, "name": "block.hanging_roots.fall"}, {"id": 692, "name": "block.hanging_roots.hit"}, {"id": 693, "name": "block.hanging_roots.place"}, {"id": 694, "name": "block.hanging_roots.step"}, {"id": 695, "name": "block.hanging_sign.step"}, {"id": 696, "name": "block.hanging_sign.break"}, {"id": 697, "name": "block.hanging_sign.fall"}, {"id": 698, "name": "block.hanging_sign.hit"}, {"id": 699, "name": "block.hanging_sign.place"}, {"id": 700, "name": "entity.happy_ghast.ambient"}, {"id": 701, "name": "entity.happy_ghast.death"}, {"id": 702, "name": "entity.happy_ghast.hurt"}, {"id": 703, "name": "entity.happy_ghast.riding"}, {"id": 704, "name": "block.heavy_core.break"}, {"id": 705, "name": "block.heavy_core.fall"}, {"id": 706, "name": "block.heavy_core.hit"}, {"id": 707, "name": "block.heavy_core.place"}, {"id": 708, "name": "block.heavy_core.step"}, {"id": 709, "name": "block.nether_wood_hanging_sign.step"}, {"id": 710, "name": "block.nether_wood_hanging_sign.break"}, {"id": 711, "name": "block.nether_wood_hanging_sign.fall"}, {"id": 712, "name": "block.nether_wood_hanging_sign.hit"}, {"id": 713, "name": "block.nether_wood_hanging_sign.place"}, {"id": 714, "name": "block.bamboo_wood_hanging_sign.step"}, {"id": 715, "name": "block.bamboo_wood_hanging_sign.break"}, {"id": 716, "name": "block.bamboo_wood_hanging_sign.fall"}, {"id": 717, "name": "block.bamboo_wood_hanging_sign.hit"}, {"id": 718, "name": "block.bamboo_wood_hanging_sign.place"}, {"id": 719, "name": "block.trial_spawner.break"}, {"id": 720, "name": "block.trial_spawner.step"}, {"id": 721, "name": "block.trial_spawner.place"}, {"id": 722, "name": "block.trial_spawner.hit"}, {"id": 723, "name": "block.trial_spawner.fall"}, {"id": 724, "name": "block.trial_spawner.spawn_mob"}, {"id": 725, "name": "block.trial_spawner.about_to_spawn_item"}, {"id": 726, "name": "block.trial_spawner.spawn_item"}, {"id": 727, "name": "block.trial_spawner.spawn_item_begin"}, {"id": 728, "name": "block.trial_spawner.detect_player"}, {"id": 729, "name": "block.trial_spawner.ominous_activate"}, {"id": 730, "name": "block.trial_spawner.ambient"}, {"id": 731, "name": "block.trial_spawner.ambient_ominous"}, {"id": 732, "name": "block.trial_spawner.open_shutter"}, {"id": 733, "name": "block.trial_spawner.close_shutter"}, {"id": 734, "name": "block.trial_spawner.eject_item"}, {"id": 735, "name": "entity.happy_ghast.equip"}, {"id": 736, "name": "entity.happy_ghast.unequip"}, {"id": 737, "name": "entity.happy_ghast.harness_goggles_up"}, {"id": 738, "name": "entity.happy_ghast.harness_goggles_down"}, {"id": 739, "name": "item.hoe.till"}, {"id": 740, "name": "entity.hoglin.ambient"}, {"id": 741, "name": "entity.hoglin.angry"}, {"id": 742, "name": "entity.hoglin.attack"}, {"id": 743, "name": "entity.hoglin.converted_to_zombified"}, {"id": 744, "name": "entity.hoglin.death"}, {"id": 745, "name": "entity.hoglin.hurt"}, {"id": 746, "name": "entity.hoglin.retreat"}, {"id": 747, "name": "entity.hoglin.step"}, {"id": 748, "name": "block.honey_block.break"}, {"id": 749, "name": "block.honey_block.fall"}, {"id": 750, "name": "block.honey_block.hit"}, {"id": 751, "name": "block.honey_block.place"}, {"id": 752, "name": "block.honey_block.slide"}, {"id": 753, "name": "block.honey_block.step"}, {"id": 754, "name": "item.honeycomb.wax_on"}, {"id": 755, "name": "item.honey_bottle.drink"}, {"id": 756, "name": "item.goat_horn.sound.0"}, {"id": 757, "name": "item.goat_horn.sound.1"}, {"id": 758, "name": "item.goat_horn.sound.2"}, {"id": 759, "name": "item.goat_horn.sound.3"}, {"id": 760, "name": "item.goat_horn.sound.4"}, {"id": 761, "name": "item.goat_horn.sound.5"}, {"id": 762, "name": "item.goat_horn.sound.6"}, {"id": 763, "name": "item.goat_horn.sound.7"}, {"id": 764, "name": "entity.horse.ambient"}, {"id": 765, "name": "entity.horse.angry"}, {"id": 766, "name": "entity.horse.armor"}, {"id": 767, "name": "item.horse_armor.unequip"}, {"id": 768, "name": "entity.horse.breathe"}, {"id": 769, "name": "entity.horse.death"}, {"id": 770, "name": "entity.horse.eat"}, {"id": 771, "name": "entity.horse.gallop"}, {"id": 772, "name": "entity.horse.hurt"}, {"id": 773, "name": "entity.horse.jump"}, {"id": 774, "name": "entity.horse.land"}, {"id": 775, "name": "entity.horse.saddle"}, {"id": 776, "name": "entity.horse.step"}, {"id": 777, "name": "entity.horse.step_wood"}, {"id": 778, "name": "entity.hostile.big_fall"}, {"id": 779, "name": "entity.hostile.death"}, {"id": 780, "name": "entity.hostile.hurt"}, {"id": 781, "name": "entity.hostile.small_fall"}, {"id": 782, "name": "entity.hostile.splash"}, {"id": 783, "name": "entity.hostile.swim"}, {"id": 784, "name": "entity.husk.ambient"}, {"id": 785, "name": "entity.husk.converted_to_zombie"}, {"id": 786, "name": "entity.husk.death"}, {"id": 787, "name": "entity.husk.hurt"}, {"id": 788, "name": "entity.husk.step"}, {"id": 789, "name": "entity.illusioner.ambient"}, {"id": 790, "name": "entity.illusioner.cast_spell"}, {"id": 791, "name": "entity.illusioner.death"}, {"id": 792, "name": "entity.illusioner.hurt"}, {"id": 793, "name": "entity.illusioner.mirror_move"}, {"id": 794, "name": "entity.illusioner.prepare_blindness"}, {"id": 795, "name": "entity.illusioner.prepare_mirror"}, {"id": 796, "name": "item.ink_sac.use"}, {"id": 797, "name": "block.iron.break"}, {"id": 798, "name": "block.iron.step"}, {"id": 799, "name": "block.iron.place"}, {"id": 800, "name": "block.iron.hit"}, {"id": 801, "name": "block.iron.fall"}, {"id": 802, "name": "block.iron_door.close"}, {"id": 803, "name": "block.iron_door.open"}, {"id": 804, "name": "entity.iron_golem.attack"}, {"id": 805, "name": "entity.iron_golem.damage"}, {"id": 806, "name": "entity.iron_golem.death"}, {"id": 807, "name": "entity.iron_golem.hurt"}, {"id": 808, "name": "entity.iron_golem.repair"}, {"id": 809, "name": "entity.iron_golem.step"}, {"id": 810, "name": "block.iron_trapdoor.close"}, {"id": 811, "name": "block.iron_trapdoor.open"}, {"id": 812, "name": "entity.item_frame.add_item"}, {"id": 813, "name": "entity.item_frame.break"}, {"id": 814, "name": "entity.item_frame.place"}, {"id": 815, "name": "entity.item_frame.remove_item"}, {"id": 816, "name": "entity.item_frame.rotate_item"}, {"id": 817, "name": "entity.item.break"}, {"id": 818, "name": "entity.item.pickup"}, {"id": 819, "name": "block.ladder.break"}, {"id": 820, "name": "block.ladder.fall"}, {"id": 821, "name": "block.ladder.hit"}, {"id": 822, "name": "block.ladder.place"}, {"id": 823, "name": "block.ladder.step"}, {"id": 824, "name": "block.lantern.break"}, {"id": 825, "name": "block.lantern.fall"}, {"id": 826, "name": "block.lantern.hit"}, {"id": 827, "name": "block.lantern.place"}, {"id": 828, "name": "block.lantern.step"}, {"id": 829, "name": "block.large_amethyst_bud.break"}, {"id": 830, "name": "block.large_amethyst_bud.place"}, {"id": 831, "name": "block.lava.ambient"}, {"id": 832, "name": "block.lava.extinguish"}, {"id": 833, "name": "block.lava.pop"}, {"id": 834, "name": "block.leaf_litter.break"}, {"id": 835, "name": "block.leaf_litter.step"}, {"id": 836, "name": "block.leaf_litter.place"}, {"id": 837, "name": "block.leaf_litter.hit"}, {"id": 838, "name": "block.leaf_litter.fall"}, {"id": 839, "name": "item.lead.untied"}, {"id": 840, "name": "item.lead.tied"}, {"id": 841, "name": "item.lead.break"}, {"id": 842, "name": "block.lever.click"}, {"id": 843, "name": "entity.lightning_bolt.impact"}, {"id": 844, "name": "entity.lightning_bolt.thunder"}, {"id": 845, "name": "entity.lingering_potion.throw"}, {"id": 846, "name": "entity.llama.ambient"}, {"id": 847, "name": "entity.llama.angry"}, {"id": 848, "name": "entity.llama.chest"}, {"id": 849, "name": "entity.llama.death"}, {"id": 850, "name": "entity.llama.eat"}, {"id": 851, "name": "entity.llama.hurt"}, {"id": 852, "name": "entity.llama.spit"}, {"id": 853, "name": "entity.llama.step"}, {"id": 854, "name": "entity.llama.swag"}, {"id": 855, "name": "item.llama_carpet.unequip"}, {"id": 856, "name": "entity.magma_cube.death_small"}, {"id": 857, "name": "block.lodestone.break"}, {"id": 858, "name": "block.lodestone.step"}, {"id": 859, "name": "block.lodestone.place"}, {"id": 860, "name": "block.lodestone.hit"}, {"id": 861, "name": "block.lodestone.fall"}, {"id": 862, "name": "item.lodestone_compass.lock"}, {"id": 863, "name": "item.mace.smash_air"}, {"id": 864, "name": "item.mace.smash_ground"}, {"id": 865, "name": "item.mace.smash_ground_heavy"}, {"id": 866, "name": "entity.magma_cube.death"}, {"id": 867, "name": "entity.magma_cube.hurt"}, {"id": 868, "name": "entity.magma_cube.hurt_small"}, {"id": 869, "name": "entity.magma_cube.jump"}, {"id": 870, "name": "entity.magma_cube.squish"}, {"id": 871, "name": "entity.magma_cube.squish_small"}, {"id": 872, "name": "block.mangrove_roots.break"}, {"id": 873, "name": "block.mangrove_roots.fall"}, {"id": 874, "name": "block.mangrove_roots.hit"}, {"id": 875, "name": "block.mangrove_roots.place"}, {"id": 876, "name": "block.mangrove_roots.step"}, {"id": 877, "name": "block.medium_amethyst_bud.break"}, {"id": 878, "name": "block.medium_amethyst_bud.place"}, {"id": 879, "name": "block.metal.break"}, {"id": 880, "name": "block.metal.fall"}, {"id": 881, "name": "block.metal.hit"}, {"id": 882, "name": "block.metal.place"}, {"id": 883, "name": "block.metal_pressure_plate.click_off"}, {"id": 884, "name": "block.metal_pressure_plate.click_on"}, {"id": 885, "name": "block.metal.step"}, {"id": 886, "name": "entity.minecart.inside.underwater"}, {"id": 887, "name": "entity.minecart.inside"}, {"id": 888, "name": "entity.minecart.riding"}, {"id": 889, "name": "entity.mooshroom.convert"}, {"id": 890, "name": "entity.mooshroom.eat"}, {"id": 891, "name": "entity.mooshroom.milk"}, {"id": 892, "name": "entity.mooshroom.suspicious_milk"}, {"id": 893, "name": "entity.mooshroom.shear"}, {"id": 894, "name": "block.moss_carpet.break"}, {"id": 895, "name": "block.moss_carpet.fall"}, {"id": 896, "name": "block.moss_carpet.hit"}, {"id": 897, "name": "block.moss_carpet.place"}, {"id": 898, "name": "block.moss_carpet.step"}, {"id": 899, "name": "block.pink_petals.break"}, {"id": 900, "name": "block.pink_petals.fall"}, {"id": 901, "name": "block.pink_petals.hit"}, {"id": 902, "name": "block.pink_petals.place"}, {"id": 903, "name": "block.pink_petals.step"}, {"id": 904, "name": "block.moss.break"}, {"id": 905, "name": "block.moss.fall"}, {"id": 906, "name": "block.moss.hit"}, {"id": 907, "name": "block.moss.place"}, {"id": 908, "name": "block.moss.step"}, {"id": 909, "name": "block.mud.break"}, {"id": 910, "name": "block.mud.fall"}, {"id": 911, "name": "block.mud.hit"}, {"id": 912, "name": "block.mud.place"}, {"id": 913, "name": "block.mud.step"}, {"id": 914, "name": "block.mud_bricks.break"}, {"id": 915, "name": "block.mud_bricks.fall"}, {"id": 916, "name": "block.mud_bricks.hit"}, {"id": 917, "name": "block.mud_bricks.place"}, {"id": 918, "name": "block.mud_bricks.step"}, {"id": 919, "name": "block.muddy_mangrove_roots.break"}, {"id": 920, "name": "block.muddy_mangrove_roots.fall"}, {"id": 921, "name": "block.muddy_mangrove_roots.hit"}, {"id": 922, "name": "block.muddy_mangrove_roots.place"}, {"id": 923, "name": "block.muddy_mangrove_roots.step"}, {"id": 924, "name": "entity.mule.ambient"}, {"id": 925, "name": "entity.mule.angry"}, {"id": 926, "name": "entity.mule.chest"}, {"id": 927, "name": "entity.mule.death"}, {"id": 928, "name": "entity.mule.eat"}, {"id": 929, "name": "entity.mule.hurt"}, {"id": 930, "name": "entity.mule.jump"}, {"id": 931, "name": "music.creative"}, {"id": 932, "name": "music.credits"}, {"id": 933, "name": "music_disc.5"}, {"id": 934, "name": "music_disc.11"}, {"id": 935, "name": "music_disc.13"}, {"id": 936, "name": "music_disc.blocks"}, {"id": 937, "name": "music_disc.cat"}, {"id": 938, "name": "music_disc.chirp"}, {"id": 939, "name": "music_disc.far"}, {"id": 940, "name": "music_disc.lava_chicken"}, {"id": 941, "name": "music_disc.mall"}, {"id": 942, "name": "music_disc.mellohi"}, {"id": 943, "name": "music_disc.pigstep"}, {"id": 944, "name": "music_disc.stal"}, {"id": 945, "name": "music_disc.strad"}, {"id": 946, "name": "music_disc.wait"}, {"id": 947, "name": "music_disc.ward"}, {"id": 948, "name": "music_disc.otherside"}, {"id": 949, "name": "music_disc.relic"}, {"id": 950, "name": "music_disc.creator"}, {"id": 951, "name": "music_disc.creator_music_box"}, {"id": 952, "name": "music_disc.precipice"}, {"id": 953, "name": "music_disc.tears"}, {"id": 954, "name": "music.dragon"}, {"id": 955, "name": "music.end"}, {"id": 956, "name": "music.game"}, {"id": 957, "name": "music.menu"}, {"id": 958, "name": "music.nether.basalt_deltas"}, {"id": 959, "name": "music.nether.crimson_forest"}, {"id": 960, "name": "music.overworld.deep_dark"}, {"id": 961, "name": "music.overworld.dripstone_caves"}, {"id": 962, "name": "music.overworld.grove"}, {"id": 963, "name": "music.overworld.jagged_peaks"}, {"id": 964, "name": "music.overworld.lush_caves"}, {"id": 965, "name": "music.overworld.swamp"}, {"id": 966, "name": "music.overworld.forest"}, {"id": 967, "name": "music.overworld.old_growth_taiga"}, {"id": 968, "name": "music.overworld.meadow"}, {"id": 969, "name": "music.overworld.cherry_grove"}, {"id": 970, "name": "music.nether.nether_wastes"}, {"id": 971, "name": "music.overworld.frozen_peaks"}, {"id": 972, "name": "music.overworld.snowy_slopes"}, {"id": 973, "name": "music.nether.soul_sand_valley"}, {"id": 974, "name": "music.overworld.stony_peaks"}, {"id": 975, "name": "music.nether.warped_forest"}, {"id": 976, "name": "music.overworld.flower_forest"}, {"id": 977, "name": "music.overworld.desert"}, {"id": 978, "name": "music.overworld.badlands"}, {"id": 979, "name": "music.overworld.jungle"}, {"id": 980, "name": "music.overworld.sparse_jungle"}, {"id": 981, "name": "music.overworld.bamboo_jungle"}, {"id": 982, "name": "music.under_water"}, {"id": 983, "name": "block.nether_bricks.break"}, {"id": 984, "name": "block.nether_bricks.step"}, {"id": 985, "name": "block.nether_bricks.place"}, {"id": 986, "name": "block.nether_bricks.hit"}, {"id": 987, "name": "block.nether_bricks.fall"}, {"id": 988, "name": "block.nether_wart.break"}, {"id": 989, "name": "item.nether_wart.plant"}, {"id": 990, "name": "block.nether_wood.break"}, {"id": 991, "name": "block.nether_wood.fall"}, {"id": 992, "name": "block.nether_wood.hit"}, {"id": 993, "name": "block.nether_wood.place"}, {"id": 994, "name": "block.nether_wood.step"}, {"id": 995, "name": "block.nether_wood_door.close"}, {"id": 996, "name": "block.nether_wood_door.open"}, {"id": 997, "name": "block.nether_wood_trapdoor.close"}, {"id": 998, "name": "block.nether_wood_trapdoor.open"}, {"id": 999, "name": "block.nether_wood_button.click_off"}, {"id": 1000, "name": "block.nether_wood_button.click_on"}, {"id": 1001, "name": "block.nether_wood_pressure_plate.click_off"}, {"id": 1002, "name": "block.nether_wood_pressure_plate.click_on"}, {"id": 1003, "name": "block.nether_wood_fence_gate.close"}, {"id": 1004, "name": "block.nether_wood_fence_gate.open"}, {"id": 1005, "name": "intentionally_empty"}, {"id": 1006, "name": "block.packed_mud.break"}, {"id": 1007, "name": "block.packed_mud.fall"}, {"id": 1008, "name": "block.packed_mud.hit"}, {"id": 1009, "name": "block.packed_mud.place"}, {"id": 1010, "name": "block.packed_mud.step"}, {"id": 1011, "name": "block.stem.break"}, {"id": 1012, "name": "block.stem.step"}, {"id": 1013, "name": "block.stem.place"}, {"id": 1014, "name": "block.stem.hit"}, {"id": 1015, "name": "block.stem.fall"}, {"id": 1016, "name": "block.nylium.break"}, {"id": 1017, "name": "block.nylium.step"}, {"id": 1018, "name": "block.nylium.place"}, {"id": 1019, "name": "block.nylium.hit"}, {"id": 1020, "name": "block.nylium.fall"}, {"id": 1021, "name": "block.nether_sprouts.break"}, {"id": 1022, "name": "block.nether_sprouts.step"}, {"id": 1023, "name": "block.nether_sprouts.place"}, {"id": 1024, "name": "block.nether_sprouts.hit"}, {"id": 1025, "name": "block.nether_sprouts.fall"}, {"id": 1026, "name": "block.fungus.break"}, {"id": 1027, "name": "block.fungus.step"}, {"id": 1028, "name": "block.fungus.place"}, {"id": 1029, "name": "block.fungus.hit"}, {"id": 1030, "name": "block.fungus.fall"}, {"id": 1031, "name": "block.weeping_vines.break"}, {"id": 1032, "name": "block.weeping_vines.step"}, {"id": 1033, "name": "block.weeping_vines.place"}, {"id": 1034, "name": "block.weeping_vines.hit"}, {"id": 1035, "name": "block.weeping_vines.fall"}, {"id": 1036, "name": "block.wart_block.break"}, {"id": 1037, "name": "block.wart_block.step"}, {"id": 1038, "name": "block.wart_block.place"}, {"id": 1039, "name": "block.wart_block.hit"}, {"id": 1040, "name": "block.wart_block.fall"}, {"id": 1041, "name": "block.netherite_block.break"}, {"id": 1042, "name": "block.netherite_block.step"}, {"id": 1043, "name": "block.netherite_block.place"}, {"id": 1044, "name": "block.netherite_block.hit"}, {"id": 1045, "name": "block.netherite_block.fall"}, {"id": 1046, "name": "block.netherrack.break"}, {"id": 1047, "name": "block.netherrack.step"}, {"id": 1048, "name": "block.netherrack.place"}, {"id": 1049, "name": "block.netherrack.hit"}, {"id": 1050, "name": "block.netherrack.fall"}, {"id": 1051, "name": "block.note_block.basedrum"}, {"id": 1052, "name": "block.note_block.bass"}, {"id": 1053, "name": "block.note_block.bell"}, {"id": 1054, "name": "block.note_block.chime"}, {"id": 1055, "name": "block.note_block.flute"}, {"id": 1056, "name": "block.note_block.guitar"}, {"id": 1057, "name": "block.note_block.harp"}, {"id": 1058, "name": "block.note_block.hat"}, {"id": 1059, "name": "block.note_block.pling"}, {"id": 1060, "name": "block.note_block.snare"}, {"id": 1061, "name": "block.note_block.xylophone"}, {"id": 1062, "name": "block.note_block.iron_xylophone"}, {"id": 1063, "name": "block.note_block.cow_bell"}, {"id": 1064, "name": "block.note_block.didgeridoo"}, {"id": 1065, "name": "block.note_block.bit"}, {"id": 1066, "name": "block.note_block.banjo"}, {"id": 1067, "name": "block.note_block.imitate.zombie"}, {"id": 1068, "name": "block.note_block.imitate.skeleton"}, {"id": 1069, "name": "block.note_block.imitate.creeper"}, {"id": 1070, "name": "block.note_block.imitate.ender_dragon"}, {"id": 1071, "name": "block.note_block.imitate.wither_skeleton"}, {"id": 1072, "name": "block.note_block.imitate.piglin"}, {"id": 1073, "name": "entity.ocelot.hurt"}, {"id": 1074, "name": "entity.ocelot.ambient"}, {"id": 1075, "name": "entity.ocelot.death"}, {"id": 1076, "name": "item.ominous_bottle.dispose"}, {"id": 1077, "name": "entity.painting.break"}, {"id": 1078, "name": "entity.painting.place"}, {"id": 1079, "name": "block.pale_hanging_moss.idle"}, {"id": 1080, "name": "entity.panda.pre_sneeze"}, {"id": 1081, "name": "entity.panda.sneeze"}, {"id": 1082, "name": "entity.panda.ambient"}, {"id": 1083, "name": "entity.panda.death"}, {"id": 1084, "name": "entity.panda.eat"}, {"id": 1085, "name": "entity.panda.step"}, {"id": 1086, "name": "entity.panda.cant_breed"}, {"id": 1087, "name": "entity.panda.aggressive_ambient"}, {"id": 1088, "name": "entity.panda.worried_ambient"}, {"id": 1089, "name": "entity.panda.hurt"}, {"id": 1090, "name": "entity.panda.bite"}, {"id": 1091, "name": "entity.parrot.ambient"}, {"id": 1092, "name": "entity.parrot.death"}, {"id": 1093, "name": "entity.parrot.eat"}, {"id": 1094, "name": "entity.parrot.fly"}, {"id": 1095, "name": "entity.parrot.hurt"}, {"id": 1096, "name": "entity.parrot.imitate.blaze"}, {"id": 1097, "name": "entity.parrot.imitate.bogged"}, {"id": 1098, "name": "entity.parrot.imitate.breeze"}, {"id": 1099, "name": "entity.parrot.imitate.creaking"}, {"id": 1100, "name": "entity.parrot.imitate.creeper"}, {"id": 1101, "name": "entity.parrot.imitate.drowned"}, {"id": 1102, "name": "entity.parrot.imitate.elder_guardian"}, {"id": 1103, "name": "entity.parrot.imitate.ender_dragon"}, {"id": 1104, "name": "entity.parrot.imitate.endermite"}, {"id": 1105, "name": "entity.parrot.imitate.evoker"}, {"id": 1106, "name": "entity.parrot.imitate.ghast"}, {"id": 1107, "name": "entity.parrot.imitate.guardian"}, {"id": 1108, "name": "entity.parrot.imitate.hoglin"}, {"id": 1109, "name": "entity.parrot.imitate.husk"}, {"id": 1110, "name": "entity.parrot.imitate.illusioner"}, {"id": 1111, "name": "entity.parrot.imitate.magma_cube"}, {"id": 1112, "name": "entity.parrot.imitate.phantom"}, {"id": 1113, "name": "entity.parrot.imitate.piglin"}, {"id": 1114, "name": "entity.parrot.imitate.piglin_brute"}, {"id": 1115, "name": "entity.parrot.imitate.pillager"}, {"id": 1116, "name": "entity.parrot.imitate.ravager"}, {"id": 1117, "name": "entity.parrot.imitate.shulker"}, {"id": 1118, "name": "entity.parrot.imitate.silverfish"}, {"id": 1119, "name": "entity.parrot.imitate.skeleton"}, {"id": 1120, "name": "entity.parrot.imitate.slime"}, {"id": 1121, "name": "entity.parrot.imitate.spider"}, {"id": 1122, "name": "entity.parrot.imitate.stray"}, {"id": 1123, "name": "entity.parrot.imitate.vex"}, {"id": 1124, "name": "entity.parrot.imitate.vindicator"}, {"id": 1125, "name": "entity.parrot.imitate.warden"}, {"id": 1126, "name": "entity.parrot.imitate.witch"}, {"id": 1127, "name": "entity.parrot.imitate.wither"}, {"id": 1128, "name": "entity.parrot.imitate.wither_skeleton"}, {"id": 1129, "name": "entity.parrot.imitate.zoglin"}, {"id": 1130, "name": "entity.parrot.imitate.zombie"}, {"id": 1131, "name": "entity.parrot.imitate.zombie_villager"}, {"id": 1132, "name": "entity.parrot.step"}, {"id": 1133, "name": "entity.phantom.ambient"}, {"id": 1134, "name": "entity.phantom.bite"}, {"id": 1135, "name": "entity.phantom.death"}, {"id": 1136, "name": "entity.phantom.flap"}, {"id": 1137, "name": "entity.phantom.hurt"}, {"id": 1138, "name": "entity.phantom.swoop"}, {"id": 1139, "name": "entity.pig.ambient"}, {"id": 1140, "name": "entity.pig.death"}, {"id": 1141, "name": "entity.pig.hurt"}, {"id": 1142, "name": "entity.pig.saddle"}, {"id": 1143, "name": "entity.pig.step"}, {"id": 1144, "name": "entity.piglin.admiring_item"}, {"id": 1145, "name": "entity.piglin.ambient"}, {"id": 1146, "name": "entity.piglin.angry"}, {"id": 1147, "name": "entity.piglin.celebrate"}, {"id": 1148, "name": "entity.piglin.death"}, {"id": 1149, "name": "entity.piglin.jealous"}, {"id": 1150, "name": "entity.piglin.hurt"}, {"id": 1151, "name": "entity.piglin.retreat"}, {"id": 1152, "name": "entity.piglin.step"}, {"id": 1153, "name": "entity.piglin.converted_to_zombified"}, {"id": 1154, "name": "entity.piglin_brute.ambient"}, {"id": 1155, "name": "entity.piglin_brute.angry"}, {"id": 1156, "name": "entity.piglin_brute.death"}, {"id": 1157, "name": "entity.piglin_brute.hurt"}, {"id": 1158, "name": "entity.piglin_brute.step"}, {"id": 1159, "name": "entity.piglin_brute.converted_to_zombified"}, {"id": 1160, "name": "entity.pillager.ambient"}, {"id": 1161, "name": "entity.pillager.celebrate"}, {"id": 1162, "name": "entity.pillager.death"}, {"id": 1163, "name": "entity.pillager.hurt"}, {"id": 1164, "name": "block.piston.contract"}, {"id": 1165, "name": "block.piston.extend"}, {"id": 1166, "name": "entity.player.attack.crit"}, {"id": 1167, "name": "entity.player.attack.knockback"}, {"id": 1168, "name": "entity.player.attack.nodamage"}, {"id": 1169, "name": "entity.player.attack.strong"}, {"id": 1170, "name": "entity.player.attack.sweep"}, {"id": 1171, "name": "entity.player.attack.weak"}, {"id": 1172, "name": "entity.player.big_fall"}, {"id": 1173, "name": "entity.player.breath"}, {"id": 1174, "name": "entity.player.burp"}, {"id": 1175, "name": "entity.player.death"}, {"id": 1176, "name": "entity.player.hurt"}, {"id": 1177, "name": "entity.player.hurt_drown"}, {"id": 1178, "name": "entity.player.hurt_freeze"}, {"id": 1179, "name": "entity.player.hurt_on_fire"}, {"id": 1180, "name": "entity.player.hurt_sweet_berry_bush"}, {"id": 1181, "name": "entity.player.levelup"}, {"id": 1182, "name": "entity.player.small_fall"}, {"id": 1183, "name": "entity.player.splash"}, {"id": 1184, "name": "entity.player.splash.high_speed"}, {"id": 1185, "name": "entity.player.swim"}, {"id": 1186, "name": "entity.player.teleport"}, {"id": 1187, "name": "entity.polar_bear.ambient"}, {"id": 1188, "name": "entity.polar_bear.ambient_baby"}, {"id": 1189, "name": "entity.polar_bear.death"}, {"id": 1190, "name": "entity.polar_bear.hurt"}, {"id": 1191, "name": "entity.polar_bear.step"}, {"id": 1192, "name": "entity.polar_bear.warning"}, {"id": 1193, "name": "block.polished_deepslate.break"}, {"id": 1194, "name": "block.polished_deepslate.fall"}, {"id": 1195, "name": "block.polished_deepslate.hit"}, {"id": 1196, "name": "block.polished_deepslate.place"}, {"id": 1197, "name": "block.polished_deepslate.step"}, {"id": 1198, "name": "block.portal.ambient"}, {"id": 1199, "name": "block.portal.travel"}, {"id": 1200, "name": "block.portal.trigger"}, {"id": 1201, "name": "block.powder_snow.break"}, {"id": 1202, "name": "block.powder_snow.fall"}, {"id": 1203, "name": "block.powder_snow.hit"}, {"id": 1204, "name": "block.powder_snow.place"}, {"id": 1205, "name": "block.powder_snow.step"}, {"id": 1206, "name": "entity.puffer_fish.blow_out"}, {"id": 1207, "name": "entity.puffer_fish.blow_up"}, {"id": 1208, "name": "entity.puffer_fish.death"}, {"id": 1209, "name": "entity.puffer_fish.flop"}, {"id": 1210, "name": "entity.puffer_fish.hurt"}, {"id": 1211, "name": "entity.puffer_fish.sting"}, {"id": 1212, "name": "block.pumpkin.carve"}, {"id": 1213, "name": "entity.rabbit.ambient"}, {"id": 1214, "name": "entity.rabbit.attack"}, {"id": 1215, "name": "entity.rabbit.death"}, {"id": 1216, "name": "entity.rabbit.hurt"}, {"id": 1217, "name": "entity.rabbit.jump"}, {"id": 1218, "name": "event.raid.horn"}, {"id": 1219, "name": "entity.ravager.ambient"}, {"id": 1220, "name": "entity.ravager.attack"}, {"id": 1221, "name": "entity.ravager.celebrate"}, {"id": 1222, "name": "entity.ravager.death"}, {"id": 1223, "name": "entity.ravager.hurt"}, {"id": 1224, "name": "entity.ravager.step"}, {"id": 1225, "name": "entity.ravager.stunned"}, {"id": 1226, "name": "entity.ravager.roar"}, {"id": 1227, "name": "block.nether_gold_ore.break"}, {"id": 1228, "name": "block.nether_gold_ore.fall"}, {"id": 1229, "name": "block.nether_gold_ore.hit"}, {"id": 1230, "name": "block.nether_gold_ore.place"}, {"id": 1231, "name": "block.nether_gold_ore.step"}, {"id": 1232, "name": "block.nether_ore.break"}, {"id": 1233, "name": "block.nether_ore.fall"}, {"id": 1234, "name": "block.nether_ore.hit"}, {"id": 1235, "name": "block.nether_ore.place"}, {"id": 1236, "name": "block.nether_ore.step"}, {"id": 1237, "name": "block.redstone_torch.burnout"}, {"id": 1238, "name": "block.respawn_anchor.ambient"}, {"id": 1239, "name": "block.respawn_anchor.charge"}, {"id": 1240, "name": "block.respawn_anchor.deplete"}, {"id": 1241, "name": "block.respawn_anchor.set_spawn"}, {"id": 1242, "name": "block.rooted_dirt.break"}, {"id": 1243, "name": "block.rooted_dirt.fall"}, {"id": 1244, "name": "block.rooted_dirt.hit"}, {"id": 1245, "name": "block.rooted_dirt.place"}, {"id": 1246, "name": "block.rooted_dirt.step"}, {"id": 1247, "name": "entity.salmon.ambient"}, {"id": 1248, "name": "entity.salmon.death"}, {"id": 1249, "name": "entity.salmon.flop"}, {"id": 1250, "name": "entity.salmon.hurt"}, {"id": 1251, "name": "block.sand.break"}, {"id": 1252, "name": "block.sand.fall"}, {"id": 1253, "name": "block.sand.hit"}, {"id": 1254, "name": "block.sand.place"}, {"id": 1255, "name": "block.sand.step"}, {"id": 1256, "name": "block.sand.idle"}, {"id": 1257, "name": "block.scaffolding.break"}, {"id": 1258, "name": "block.scaffolding.fall"}, {"id": 1259, "name": "block.scaffolding.hit"}, {"id": 1260, "name": "block.scaffolding.place"}, {"id": 1261, "name": "block.scaffolding.step"}, {"id": 1262, "name": "block.sculk.spread"}, {"id": 1263, "name": "block.sculk.charge"}, {"id": 1264, "name": "block.sculk.break"}, {"id": 1265, "name": "block.sculk.fall"}, {"id": 1266, "name": "block.sculk.hit"}, {"id": 1267, "name": "block.sculk.place"}, {"id": 1268, "name": "block.sculk.step"}, {"id": 1269, "name": "block.sculk_catalyst.bloom"}, {"id": 1270, "name": "block.sculk_catalyst.break"}, {"id": 1271, "name": "block.sculk_catalyst.fall"}, {"id": 1272, "name": "block.sculk_catalyst.hit"}, {"id": 1273, "name": "block.sculk_catalyst.place"}, {"id": 1274, "name": "block.sculk_catalyst.step"}, {"id": 1275, "name": "block.sculk_sensor.clicking"}, {"id": 1276, "name": "block.sculk_sensor.clicking_stop"}, {"id": 1277, "name": "block.sculk_sensor.break"}, {"id": 1278, "name": "block.sculk_sensor.fall"}, {"id": 1279, "name": "block.sculk_sensor.hit"}, {"id": 1280, "name": "block.sculk_sensor.place"}, {"id": 1281, "name": "block.sculk_sensor.step"}, {"id": 1282, "name": "block.sculk_shrieker.break"}, {"id": 1283, "name": "block.sculk_shrieker.fall"}, {"id": 1284, "name": "block.sculk_shrieker.hit"}, {"id": 1285, "name": "block.sculk_shrieker.place"}, {"id": 1286, "name": "block.sculk_shrieker.shriek"}, {"id": 1287, "name": "block.sculk_shrieker.step"}, {"id": 1288, "name": "block.sculk_vein.break"}, {"id": 1289, "name": "block.sculk_vein.fall"}, {"id": 1290, "name": "block.sculk_vein.hit"}, {"id": 1291, "name": "block.sculk_vein.place"}, {"id": 1292, "name": "block.sculk_vein.step"}, {"id": 1293, "name": "entity.sheep.ambient"}, {"id": 1294, "name": "entity.sheep.death"}, {"id": 1295, "name": "entity.sheep.hurt"}, {"id": 1296, "name": "entity.sheep.shear"}, {"id": 1297, "name": "entity.sheep.step"}, {"id": 1298, "name": "item.shears.snip"}, {"id": 1299, "name": "item.shield.block"}, {"id": 1300, "name": "item.shield.break"}, {"id": 1301, "name": "block.shroomlight.break"}, {"id": 1302, "name": "block.shroomlight.step"}, {"id": 1303, "name": "block.shroomlight.place"}, {"id": 1304, "name": "block.shroomlight.hit"}, {"id": 1305, "name": "block.shroomlight.fall"}, {"id": 1306, "name": "item.shovel.flatten"}, {"id": 1307, "name": "entity.shulker.ambient"}, {"id": 1308, "name": "block.shulker_box.close"}, {"id": 1309, "name": "block.shulker_box.open"}, {"id": 1310, "name": "entity.shulker_bullet.hit"}, {"id": 1311, "name": "entity.shulker_bullet.hurt"}, {"id": 1312, "name": "entity.shulker.close"}, {"id": 1313, "name": "entity.shulker.death"}, {"id": 1314, "name": "entity.shulker.hurt"}, {"id": 1315, "name": "entity.shulker.hurt_closed"}, {"id": 1316, "name": "entity.shulker.open"}, {"id": 1317, "name": "entity.shulker.shoot"}, {"id": 1318, "name": "entity.shulker.teleport"}, {"id": 1319, "name": "entity.silverfish.ambient"}, {"id": 1320, "name": "entity.silverfish.death"}, {"id": 1321, "name": "entity.silverfish.hurt"}, {"id": 1322, "name": "entity.silverfish.step"}, {"id": 1323, "name": "entity.skeleton.ambient"}, {"id": 1324, "name": "entity.skeleton.converted_to_stray"}, {"id": 1325, "name": "entity.skeleton.death"}, {"id": 1326, "name": "entity.skeleton_horse.ambient"}, {"id": 1327, "name": "entity.skeleton_horse.death"}, {"id": 1328, "name": "entity.skeleton_horse.hurt"}, {"id": 1329, "name": "entity.skeleton_horse.swim"}, {"id": 1330, "name": "entity.skeleton_horse.ambient_water"}, {"id": 1331, "name": "entity.skeleton_horse.gallop_water"}, {"id": 1332, "name": "entity.skeleton_horse.jump_water"}, {"id": 1333, "name": "entity.skeleton_horse.step_water"}, {"id": 1334, "name": "entity.skeleton.hurt"}, {"id": 1335, "name": "entity.skeleton.shoot"}, {"id": 1336, "name": "entity.skeleton.step"}, {"id": 1337, "name": "entity.slime.attack"}, {"id": 1338, "name": "entity.slime.death"}, {"id": 1339, "name": "entity.slime.hurt"}, {"id": 1340, "name": "entity.slime.jump"}, {"id": 1341, "name": "entity.slime.squish"}, {"id": 1342, "name": "block.slime_block.break"}, {"id": 1343, "name": "block.slime_block.fall"}, {"id": 1344, "name": "block.slime_block.hit"}, {"id": 1345, "name": "block.slime_block.place"}, {"id": 1346, "name": "block.slime_block.step"}, {"id": 1347, "name": "block.small_amethyst_bud.break"}, {"id": 1348, "name": "block.small_amethyst_bud.place"}, {"id": 1349, "name": "block.small_dripleaf.break"}, {"id": 1350, "name": "block.small_dripleaf.fall"}, {"id": 1351, "name": "block.small_dripleaf.hit"}, {"id": 1352, "name": "block.small_dripleaf.place"}, {"id": 1353, "name": "block.small_dripleaf.step"}, {"id": 1354, "name": "block.soul_sand.break"}, {"id": 1355, "name": "block.soul_sand.step"}, {"id": 1356, "name": "block.soul_sand.place"}, {"id": 1357, "name": "block.soul_sand.hit"}, {"id": 1358, "name": "block.soul_sand.fall"}, {"id": 1359, "name": "block.soul_soil.break"}, {"id": 1360, "name": "block.soul_soil.step"}, {"id": 1361, "name": "block.soul_soil.place"}, {"id": 1362, "name": "block.soul_soil.hit"}, {"id": 1363, "name": "block.soul_soil.fall"}, {"id": 1364, "name": "particle.soul_escape"}, {"id": 1365, "name": "block.spawner.break"}, {"id": 1366, "name": "block.spawner.fall"}, {"id": 1367, "name": "block.spawner.hit"}, {"id": 1368, "name": "block.spawner.place"}, {"id": 1369, "name": "block.spawner.step"}, {"id": 1370, "name": "block.resin.break"}, {"id": 1371, "name": "block.resin.fall"}, {"id": 1372, "name": "block.resin.place"}, {"id": 1373, "name": "block.resin.step"}, {"id": 1374, "name": "block.resin_bricks.break"}, {"id": 1375, "name": "block.resin_bricks.fall"}, {"id": 1376, "name": "block.resin_bricks.hit"}, {"id": 1377, "name": "block.resin_bricks.place"}, {"id": 1378, "name": "block.resin_bricks.step"}, {"id": 1379, "name": "block.spore_blossom.break"}, {"id": 1380, "name": "block.spore_blossom.fall"}, {"id": 1381, "name": "block.spore_blossom.hit"}, {"id": 1382, "name": "block.spore_blossom.place"}, {"id": 1383, "name": "block.spore_blossom.step"}, {"id": 1384, "name": "entity.strider.ambient"}, {"id": 1385, "name": "entity.strider.happy"}, {"id": 1386, "name": "entity.strider.retreat"}, {"id": 1387, "name": "entity.strider.death"}, {"id": 1388, "name": "entity.strider.hurt"}, {"id": 1389, "name": "entity.strider.step"}, {"id": 1390, "name": "entity.strider.step_lava"}, {"id": 1391, "name": "entity.strider.eat"}, {"id": 1392, "name": "entity.strider.saddle"}, {"id": 1393, "name": "entity.slime.death_small"}, {"id": 1394, "name": "entity.slime.hurt_small"}, {"id": 1395, "name": "entity.slime.jump_small"}, {"id": 1396, "name": "entity.slime.squish_small"}, {"id": 1397, "name": "block.smithing_table.use"}, {"id": 1398, "name": "block.smoker.smoke"}, {"id": 1399, "name": "entity.sniffer.step"}, {"id": 1400, "name": "entity.sniffer.eat"}, {"id": 1401, "name": "entity.sniffer.idle"}, {"id": 1402, "name": "entity.sniffer.hurt"}, {"id": 1403, "name": "entity.sniffer.death"}, {"id": 1404, "name": "entity.sniffer.drop_seed"}, {"id": 1405, "name": "entity.sniffer.scenting"}, {"id": 1406, "name": "entity.sniffer.sniffing"}, {"id": 1407, "name": "entity.sniffer.searching"}, {"id": 1408, "name": "entity.sniffer.digging"}, {"id": 1409, "name": "entity.sniffer.digging_stop"}, {"id": 1410, "name": "entity.sniffer.happy"}, {"id": 1411, "name": "block.sniffer_egg.plop"}, {"id": 1412, "name": "block.sniffer_egg.crack"}, {"id": 1413, "name": "block.sniffer_egg.hatch"}, {"id": 1414, "name": "entity.snowball.throw"}, {"id": 1415, "name": "block.snow.break"}, {"id": 1416, "name": "block.snow.fall"}, {"id": 1417, "name": "entity.snow_golem.ambient"}, {"id": 1418, "name": "entity.snow_golem.death"}, {"id": 1419, "name": "entity.snow_golem.hurt"}, {"id": 1420, "name": "entity.snow_golem.shoot"}, {"id": 1421, "name": "entity.snow_golem.shear"}, {"id": 1422, "name": "block.snow.hit"}, {"id": 1423, "name": "block.snow.place"}, {"id": 1424, "name": "block.snow.step"}, {"id": 1425, "name": "entity.spider.ambient"}, {"id": 1426, "name": "entity.spider.death"}, {"id": 1427, "name": "entity.spider.hurt"}, {"id": 1428, "name": "entity.spider.step"}, {"id": 1429, "name": "entity.splash_potion.break"}, {"id": 1430, "name": "entity.splash_potion.throw"}, {"id": 1431, "name": "block.sponge.break"}, {"id": 1432, "name": "block.sponge.fall"}, {"id": 1433, "name": "block.sponge.hit"}, {"id": 1434, "name": "block.sponge.place"}, {"id": 1435, "name": "block.sponge.step"}, {"id": 1436, "name": "block.sponge.absorb"}, {"id": 1437, "name": "item.spyglass.use"}, {"id": 1438, "name": "item.spyglass.stop_using"}, {"id": 1439, "name": "entity.squid.ambient"}, {"id": 1440, "name": "entity.squid.death"}, {"id": 1441, "name": "entity.squid.hurt"}, {"id": 1442, "name": "entity.squid.squirt"}, {"id": 1443, "name": "block.stone.break"}, {"id": 1444, "name": "block.stone_button.click_off"}, {"id": 1445, "name": "block.stone_button.click_on"}, {"id": 1446, "name": "block.stone.fall"}, {"id": 1447, "name": "block.stone.hit"}, {"id": 1448, "name": "block.stone.place"}, {"id": 1449, "name": "block.stone_pressure_plate.click_off"}, {"id": 1450, "name": "block.stone_pressure_plate.click_on"}, {"id": 1451, "name": "block.stone.step"}, {"id": 1452, "name": "entity.stray.ambient"}, {"id": 1453, "name": "entity.stray.death"}, {"id": 1454, "name": "entity.stray.hurt"}, {"id": 1455, "name": "entity.stray.step"}, {"id": 1456, "name": "block.sweet_berry_bush.break"}, {"id": 1457, "name": "block.sweet_berry_bush.place"}, {"id": 1458, "name": "block.sweet_berry_bush.pick_berries"}, {"id": 1459, "name": "entity.tadpole.death"}, {"id": 1460, "name": "entity.tadpole.flop"}, {"id": 1461, "name": "entity.tadpole.grow_up"}, {"id": 1462, "name": "entity.tadpole.hurt"}, {"id": 1463, "name": "enchant.thorns.hit"}, {"id": 1464, "name": "entity.tnt.primed"}, {"id": 1465, "name": "item.totem.use"}, {"id": 1466, "name": "item.trident.hit"}, {"id": 1467, "name": "item.trident.hit_ground"}, {"id": 1468, "name": "item.trident.return"}, {"id": 1469, "name": "item.trident.riptide_1"}, {"id": 1470, "name": "item.trident.riptide_2"}, {"id": 1471, "name": "item.trident.riptide_3"}, {"id": 1472, "name": "item.trident.throw"}, {"id": 1473, "name": "item.trident.thunder"}, {"id": 1474, "name": "block.tripwire.attach"}, {"id": 1475, "name": "block.tripwire.click_off"}, {"id": 1476, "name": "block.tripwire.click_on"}, {"id": 1477, "name": "block.tripwire.detach"}, {"id": 1478, "name": "entity.tropical_fish.ambient"}, {"id": 1479, "name": "entity.tropical_fish.death"}, {"id": 1480, "name": "entity.tropical_fish.flop"}, {"id": 1481, "name": "entity.tropical_fish.hurt"}, {"id": 1482, "name": "block.tuff.break"}, {"id": 1483, "name": "block.tuff.step"}, {"id": 1484, "name": "block.tuff.place"}, {"id": 1485, "name": "block.tuff.hit"}, {"id": 1486, "name": "block.tuff.fall"}, {"id": 1487, "name": "block.tuff_bricks.break"}, {"id": 1488, "name": "block.tuff_bricks.fall"}, {"id": 1489, "name": "block.tuff_bricks.hit"}, {"id": 1490, "name": "block.tuff_bricks.place"}, {"id": 1491, "name": "block.tuff_bricks.step"}, {"id": 1492, "name": "block.polished_tuff.break"}, {"id": 1493, "name": "block.polished_tuff.fall"}, {"id": 1494, "name": "block.polished_tuff.hit"}, {"id": 1495, "name": "block.polished_tuff.place"}, {"id": 1496, "name": "block.polished_tuff.step"}, {"id": 1497, "name": "entity.turtle.ambient_land"}, {"id": 1498, "name": "entity.turtle.death"}, {"id": 1499, "name": "entity.turtle.death_baby"}, {"id": 1500, "name": "entity.turtle.egg_break"}, {"id": 1501, "name": "entity.turtle.egg_crack"}, {"id": 1502, "name": "entity.turtle.egg_hatch"}, {"id": 1503, "name": "entity.turtle.hurt"}, {"id": 1504, "name": "entity.turtle.hurt_baby"}, {"id": 1505, "name": "entity.turtle.lay_egg"}, {"id": 1506, "name": "entity.turtle.shamble"}, {"id": 1507, "name": "entity.turtle.shamble_baby"}, {"id": 1508, "name": "entity.turtle.swim"}, {"id": 1509, "name": "ui.button.click"}, {"id": 1510, "name": "ui.loom.select_pattern"}, {"id": 1511, "name": "ui.loom.take_result"}, {"id": 1512, "name": "ui.cartography_table.take_result"}, {"id": 1513, "name": "ui.stonecutter.take_result"}, {"id": 1514, "name": "ui.stonecutter.select_recipe"}, {"id": 1515, "name": "ui.toast.challenge_complete"}, {"id": 1516, "name": "ui.toast.in"}, {"id": 1517, "name": "ui.toast.out"}, {"id": 1518, "name": "block.vault.activate"}, {"id": 1519, "name": "block.vault.ambient"}, {"id": 1520, "name": "block.vault.break"}, {"id": 1521, "name": "block.vault.close_shutter"}, {"id": 1522, "name": "block.vault.deactivate"}, {"id": 1523, "name": "block.vault.eject_item"}, {"id": 1524, "name": "block.vault.reject_rewarded_player"}, {"id": 1525, "name": "block.vault.fall"}, {"id": 1526, "name": "block.vault.hit"}, {"id": 1527, "name": "block.vault.insert_item"}, {"id": 1528, "name": "block.vault.insert_item_fail"}, {"id": 1529, "name": "block.vault.open_shutter"}, {"id": 1530, "name": "block.vault.place"}, {"id": 1531, "name": "block.vault.step"}, {"id": 1532, "name": "entity.vex.ambient"}, {"id": 1533, "name": "entity.vex.charge"}, {"id": 1534, "name": "entity.vex.death"}, {"id": 1535, "name": "entity.vex.hurt"}, {"id": 1536, "name": "entity.villager.ambient"}, {"id": 1537, "name": "entity.villager.celebrate"}, {"id": 1538, "name": "entity.villager.death"}, {"id": 1539, "name": "entity.villager.hurt"}, {"id": 1540, "name": "entity.villager.no"}, {"id": 1541, "name": "entity.villager.trade"}, {"id": 1542, "name": "entity.villager.yes"}, {"id": 1543, "name": "entity.villager.work_armorer"}, {"id": 1544, "name": "entity.villager.work_butcher"}, {"id": 1545, "name": "entity.villager.work_cartographer"}, {"id": 1546, "name": "entity.villager.work_cleric"}, {"id": 1547, "name": "entity.villager.work_farmer"}, {"id": 1548, "name": "entity.villager.work_fisherman"}, {"id": 1549, "name": "entity.villager.work_fletcher"}, {"id": 1550, "name": "entity.villager.work_leatherworker"}, {"id": 1551, "name": "entity.villager.work_librarian"}, {"id": 1552, "name": "entity.villager.work_mason"}, {"id": 1553, "name": "entity.villager.work_shepherd"}, {"id": 1554, "name": "entity.villager.work_toolsmith"}, {"id": 1555, "name": "entity.villager.work_weaponsmith"}, {"id": 1556, "name": "entity.vindicator.ambient"}, {"id": 1557, "name": "entity.vindicator.celebrate"}, {"id": 1558, "name": "entity.vindicator.death"}, {"id": 1559, "name": "entity.vindicator.hurt"}, {"id": 1560, "name": "block.vine.break"}, {"id": 1561, "name": "block.vine.fall"}, {"id": 1562, "name": "block.vine.hit"}, {"id": 1563, "name": "block.vine.place"}, {"id": 1564, "name": "block.vine.step"}, {"id": 1565, "name": "block.lily_pad.place"}, {"id": 1566, "name": "entity.wandering_trader.ambient"}, {"id": 1567, "name": "entity.wandering_trader.death"}, {"id": 1568, "name": "entity.wandering_trader.disappeared"}, {"id": 1569, "name": "entity.wandering_trader.drink_milk"}, {"id": 1570, "name": "entity.wandering_trader.drink_potion"}, {"id": 1571, "name": "entity.wandering_trader.hurt"}, {"id": 1572, "name": "entity.wandering_trader.no"}, {"id": 1573, "name": "entity.wandering_trader.reappeared"}, {"id": 1574, "name": "entity.wandering_trader.trade"}, {"id": 1575, "name": "entity.wandering_trader.yes"}, {"id": 1576, "name": "entity.warden.agitated"}, {"id": 1577, "name": "entity.warden.ambient"}, {"id": 1578, "name": "entity.warden.angry"}, {"id": 1579, "name": "entity.warden.attack_impact"}, {"id": 1580, "name": "entity.warden.death"}, {"id": 1581, "name": "entity.warden.dig"}, {"id": 1582, "name": "entity.warden.emerge"}, {"id": 1583, "name": "entity.warden.heartbeat"}, {"id": 1584, "name": "entity.warden.hurt"}, {"id": 1585, "name": "entity.warden.listening"}, {"id": 1586, "name": "entity.warden.listening_angry"}, {"id": 1587, "name": "entity.warden.nearby_close"}, {"id": 1588, "name": "entity.warden.nearby_closer"}, {"id": 1589, "name": "entity.warden.nearby_closest"}, {"id": 1590, "name": "entity.warden.roar"}, {"id": 1591, "name": "entity.warden.sniff"}, {"id": 1592, "name": "entity.warden.sonic_boom"}, {"id": 1593, "name": "entity.warden.sonic_charge"}, {"id": 1594, "name": "entity.warden.step"}, {"id": 1595, "name": "entity.warden.tendril_clicks"}, {"id": 1596, "name": "block.hanging_sign.waxed_interact_fail"}, {"id": 1597, "name": "block.sign.waxed_interact_fail"}, {"id": 1598, "name": "block.water.ambient"}, {"id": 1599, "name": "weather.rain"}, {"id": 1600, "name": "weather.rain.above"}, {"id": 1601, "name": "block.wet_grass.break"}, {"id": 1602, "name": "block.wet_grass.fall"}, {"id": 1603, "name": "block.wet_grass.hit"}, {"id": 1604, "name": "block.wet_grass.place"}, {"id": 1605, "name": "block.wet_grass.step"}, {"id": 1606, "name": "block.wet_sponge.break"}, {"id": 1607, "name": "block.wet_sponge.dries"}, {"id": 1608, "name": "block.wet_sponge.fall"}, {"id": 1609, "name": "block.wet_sponge.hit"}, {"id": 1610, "name": "block.wet_sponge.place"}, {"id": 1611, "name": "block.wet_sponge.step"}, {"id": 1612, "name": "entity.wind_charge.wind_burst"}, {"id": 1613, "name": "entity.wind_charge.throw"}, {"id": 1614, "name": "entity.witch.ambient"}, {"id": 1615, "name": "entity.witch.celebrate"}, {"id": 1616, "name": "entity.witch.death"}, {"id": 1617, "name": "entity.witch.drink"}, {"id": 1618, "name": "entity.witch.hurt"}, {"id": 1619, "name": "entity.witch.throw"}, {"id": 1620, "name": "entity.wither.ambient"}, {"id": 1621, "name": "entity.wither.break_block"}, {"id": 1622, "name": "entity.wither.death"}, {"id": 1623, "name": "entity.wither.hurt"}, {"id": 1624, "name": "entity.wither.shoot"}, {"id": 1625, "name": "entity.wither_skeleton.ambient"}, {"id": 1626, "name": "entity.wither_skeleton.death"}, {"id": 1627, "name": "entity.wither_skeleton.hurt"}, {"id": 1628, "name": "entity.wither_skeleton.step"}, {"id": 1629, "name": "entity.wither.spawn"}, {"id": 1630, "name": "item.wolf_armor.break"}, {"id": 1631, "name": "item.wolf_armor.crack"}, {"id": 1632, "name": "item.wolf_armor.damage"}, {"id": 1633, "name": "item.wolf_armor.repair"}, {"id": 1634, "name": "entity.wolf.shake"}, {"id": 1635, "name": "entity.wolf.step"}, {"id": 1636, "name": "entity.wolf.ambient"}, {"id": 1637, "name": "entity.wolf.death"}, {"id": 1638, "name": "entity.wolf.growl"}, {"id": 1639, "name": "entity.wolf.hurt"}, {"id": 1640, "name": "entity.wolf.pant"}, {"id": 1641, "name": "entity.wolf.whine"}, {"id": 1642, "name": "entity.wolf_puglin.ambient"}, {"id": 1643, "name": "entity.wolf_puglin.death"}, {"id": 1644, "name": "entity.wolf_puglin.growl"}, {"id": 1645, "name": "entity.wolf_puglin.hurt"}, {"id": 1646, "name": "entity.wolf_puglin.pant"}, {"id": 1647, "name": "entity.wolf_puglin.whine"}, {"id": 1648, "name": "entity.wolf_sad.ambient"}, {"id": 1649, "name": "entity.wolf_sad.death"}, {"id": 1650, "name": "entity.wolf_sad.growl"}, {"id": 1651, "name": "entity.wolf_sad.hurt"}, {"id": 1652, "name": "entity.wolf_sad.pant"}, {"id": 1653, "name": "entity.wolf_sad.whine"}, {"id": 1654, "name": "entity.wolf_angry.ambient"}, {"id": 1655, "name": "entity.wolf_angry.death"}, {"id": 1656, "name": "entity.wolf_angry.growl"}, {"id": 1657, "name": "entity.wolf_angry.hurt"}, {"id": 1658, "name": "entity.wolf_angry.pant"}, {"id": 1659, "name": "entity.wolf_angry.whine"}, {"id": 1660, "name": "entity.wolf_grumpy.ambient"}, {"id": 1661, "name": "entity.wolf_grumpy.death"}, {"id": 1662, "name": "entity.wolf_grumpy.growl"}, {"id": 1663, "name": "entity.wolf_grumpy.hurt"}, {"id": 1664, "name": "entity.wolf_grumpy.pant"}, {"id": 1665, "name": "entity.wolf_grumpy.whine"}, {"id": 1666, "name": "entity.wolf_big.ambient"}, {"id": 1667, "name": "entity.wolf_big.death"}, {"id": 1668, "name": "entity.wolf_big.growl"}, {"id": 1669, "name": "entity.wolf_big.hurt"}, {"id": 1670, "name": "entity.wolf_big.pant"}, {"id": 1671, "name": "entity.wolf_big.whine"}, {"id": 1672, "name": "entity.wolf_cute.ambient"}, {"id": 1673, "name": "entity.wolf_cute.death"}, {"id": 1674, "name": "entity.wolf_cute.growl"}, {"id": 1675, "name": "entity.wolf_cute.hurt"}, {"id": 1676, "name": "entity.wolf_cute.pant"}, {"id": 1677, "name": "entity.wolf_cute.whine"}, {"id": 1678, "name": "block.wooden_door.close"}, {"id": 1679, "name": "block.wooden_door.open"}, {"id": 1680, "name": "block.wooden_trapdoor.close"}, {"id": 1681, "name": "block.wooden_trapdoor.open"}, {"id": 1682, "name": "block.wooden_button.click_off"}, {"id": 1683, "name": "block.wooden_button.click_on"}, {"id": 1684, "name": "block.wooden_pressure_plate.click_off"}, {"id": 1685, "name": "block.wooden_pressure_plate.click_on"}, {"id": 1686, "name": "block.wood.break"}, {"id": 1687, "name": "block.wood.fall"}, {"id": 1688, "name": "block.wood.hit"}, {"id": 1689, "name": "block.wood.place"}, {"id": 1690, "name": "block.wood.step"}, {"id": 1691, "name": "block.wool.break"}, {"id": 1692, "name": "block.wool.fall"}, {"id": 1693, "name": "block.wool.hit"}, {"id": 1694, "name": "block.wool.place"}, {"id": 1695, "name": "block.wool.step"}, {"id": 1696, "name": "entity.zoglin.ambient"}, {"id": 1697, "name": "entity.zoglin.angry"}, {"id": 1698, "name": "entity.zoglin.attack"}, {"id": 1699, "name": "entity.zoglin.death"}, {"id": 1700, "name": "entity.zoglin.hurt"}, {"id": 1701, "name": "entity.zoglin.step"}, {"id": 1702, "name": "entity.zombie.ambient"}, {"id": 1703, "name": "entity.zombie.attack_wooden_door"}, {"id": 1704, "name": "entity.zombie.attack_iron_door"}, {"id": 1705, "name": "entity.zombie.break_wooden_door"}, {"id": 1706, "name": "entity.zombie.converted_to_drowned"}, {"id": 1707, "name": "entity.zombie.death"}, {"id": 1708, "name": "entity.zombie.destroy_egg"}, {"id": 1709, "name": "entity.zombie_horse.ambient"}, {"id": 1710, "name": "entity.zombie_horse.death"}, {"id": 1711, "name": "entity.zombie_horse.hurt"}, {"id": 1712, "name": "entity.zombie.hurt"}, {"id": 1713, "name": "entity.zombie.infect"}, {"id": 1714, "name": "entity.zombified_piglin.ambient"}, {"id": 1715, "name": "entity.zombified_piglin.angry"}, {"id": 1716, "name": "entity.zombified_piglin.death"}, {"id": 1717, "name": "entity.zombified_piglin.hurt"}, {"id": 1718, "name": "entity.zombie.step"}, {"id": 1719, "name": "entity.zombie_villager.ambient"}, {"id": 1720, "name": "entity.zombie_villager.converted"}, {"id": 1721, "name": "entity.zombie_villager.cure"}, {"id": 1722, "name": "entity.zombie_villager.death"}, {"id": 1723, "name": "entity.zombie_villager.hurt"}, {"id": 1724, "name": "entity.zombie_villager.step"}, {"id": 1725, "name": "event.mob_effect.bad_omen"}, {"id": 1726, "name": "event.mob_effect.trial_omen"}, {"id": 1727, "name": "event.mob_effect.raid_omen"}, {"id": 1728, "name": "item.saddle.unequip"}]