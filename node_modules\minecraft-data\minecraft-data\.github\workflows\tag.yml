name: 'tag'
on:
  push:
    branches:
      - master
jobs:
  tag:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions-ecosystem/action-regex-match@v2
      id: regex-match
      with:
        text: ${{ github.event.head_commit.message }}
        regex: '^Release ([0-9]+\.[0-9]+\.[0-9]+)'
    - name: Create Release
      if: steps.regex-match.outputs.match != ''
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.regex-match.outputs.group1 }}
        release_name: Release ${{ steps.regex-match.outputs.group1 }}
        body: ${{ steps.regex-match.outputs.group1 }}
        draft: false
        prerelease: false
    - uses: peter-evans/repository-dispatch@v1
      if: ${{ steps.regex-match.outputs.match != '' }}
      with:
        token: ${{ secrets.PAT_PASSWORD }}
        repository: PrismarineJS/node-minecraft-data
        event-type: mcData-release
        client-payload: '{"tag": "${{ steps.regex-match.outputs.group1 }}"}'
