const mineflayer = require('mineflayer')

const bot = mineflayer.createBot({
  host: 'mc.kartoplyaniy.space', // minecraft server ip
  username: '<PERSON><PERSON><PERSON>', // username to join as if auth is `offline`, else a unique identifier for this account. Switch if you want to change accounts
  auth: 'offline', // for offline mode servers, you can set this to 'offline'
  port: 25565, // default minecraft port
  version: false, // automatically detect server version
  skipValidation: true, // skip validation to avoid resource pack issues
  hideErrors: false // show errors for debugging
})

// Handle resource pack requests - decline them to avoid loading issues
bot.on('resourcePack', (url, hash) => {
  console.log('Server requested resource pack, declining...')
  bot.denyResourcePack()
})

// Handle successful login
bot.on('login', () => {
  console.log(`Bo<PERSON> logged in as ${bot.username} to ${bot.options.host}`)
})

bot.on('chat', (username, message) => {
  if (username === bot.username) return
  bot.chat(message)
})

// Log errors and kick reasons:
bot.on('kicked', (reason) => {
  console.log('<PERSON><PERSON> was kicked:', reason)
})

bot.on('error', (err) => {
  console.log('Bot error:', err)
})