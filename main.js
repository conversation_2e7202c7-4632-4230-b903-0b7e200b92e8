const mineflayer = require('mineflayer')

const bot = mineflayer.createBot({
  host: 'mc.kartoplyaniy.space',
  username: '<PERSON><PERSON><PERSON>',
  auth: 'offline'
})

// Listen for the resource_pack_send packet directly
bot._client.on('resource_pack_send', (packet) => {
  console.log('Відхиляю ресурс-пак сервера...')

  // We must send a resource_pack_receive packet back to the server
  // Access the underlying minecraft-protocol client
  bot._client.write('resource_pack_receive', {
    id: packet.id, // Use the 'id' field from the incoming packet
    result: 3 // 'Declined'
  })
})

bot.on('login', () => {
    console.log('✅ Бот підключається...')
})

bot.on('spawn', () => {
    console.log('🎉 Бот успішно підключився та завантажився в світ!')
})

bot.on('chat', (username, message) => {
  if (username === bot.username) return
  bot.chat(message)
})

bot.on('kicked', console.log)
bot.on('error', console.log)
bot.on('end', (reason) => {
    console.log(`❌ Бот відключився. Причина: ${reason}`)
})