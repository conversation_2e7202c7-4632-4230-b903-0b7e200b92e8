const mineflayer = require('mineflayer')

const bot = mineflayer.createBot({
  host: 'mc.kartoplyaniy.space', // minecraft server ip
  username: '<PERSON><PERSON><PERSON>', // username to join as if auth is `offline`, else a unique identifier for this account. Switch if you want to change accounts
  auth: 'offline', // for offline mode servers, you can set this to 'offline'
  port: 25565, // default minecraft port
  version: false, // automatically detect server version
  skipValidation: true, // skip validation to avoid resource pack issues
  hideErrors: false, // show errors for debugging
  checkTimeoutInterval: 30000, // increase timeout
  keepAlive: true, // keep connection alive
  respawn: true // auto respawn if died
})

// Handle resource pack requests - accept them to avoid being kicked
bot.on('resourcePack', (url, hash) => {
  console.log('Server requested resource pack, accepting...')
  console.log('Resource pack URL:', url)
  console.log('Resource pack hash:', hash)

  // Accept the resource pack to avoid being kicked
  try {
    bot.acceptResourcePack()
    console.log('Resource pack accepted successfully')

    // Simulate resource pack download completion after a delay
    setTimeout(() => {
      try {
        console.log('Simulating resource pack download completion...')
        bot._client.write('resource_pack_receive', {
          result: 0 // 0 = successfully downloaded
        })
        console.log('Sent resource pack completion status')
      } catch (completeErr) {
        console.log('Error sending completion status:', completeErr.message)
      }
    }, 2000)

  } catch (err) {
    console.log('Error accepting resource pack:', err.message)

    // Try alternative method - send raw packet
    try {
      bot._client.write('resource_pack_receive', {
        result: 0 // 0 = successfully downloaded
      })
      console.log('Sent resource pack status via raw packet')
    } catch (rawErr) {
      console.log('Raw packet method also failed:', rawErr.message)
    }
  }
})

// Handle resource pack download progress (if it happens)
bot.on('resourcePackProgress', (progress) => {
  console.log(`Resource pack download progress: ${progress}%`)
})

// Handle successful login
bot.on('login', () => {
  console.log(`Bot logged in as ${bot.username} to ${bot.options.host}`)
  console.log('Waiting for spawn...')
})

// Handle connection state changes
bot.on('connect', () => {
  console.log('Bot connected to server')
})

bot.on('disconnect', (packet) => {
  console.log('Bot disconnected:', packet.reason)
})

bot.on('end', (reason) => {
  console.log('Connection ended:', reason)
})

// Handle spawn event
bot.on('spawn', () => {
  console.log('Bot spawned in the world!')

  // Send login command once after spawning
  setTimeout(() => {
    console.log('Sending login command...')
    bot.chat('/login as2mo5dot183')
    console.log('Login command sent!')
  }, 1000)
})

// Add more detailed logging
bot.on('playerJoined', (player) => {
  console.log(`Player joined: ${player.username}`)
})

bot.on('playerLeft', (player) => {
  console.log(`Player left: ${player.username}`)
})

bot.on('health', () => {
  console.log(`Bot health: ${bot.health}, food: ${bot.food}`)
})

bot.on('death', () => {
  console.log('Bot died!')
})

bot.on('respawn', () => {
  console.log('Bot respawned!')
})

bot.on('chat', (username, message) => {
  if (username === bot.username) return
  bot.chat(message)
})

// Log errors and kick reasons:
bot.on('kicked', (reason) => {
  console.log('Bot was kicked:', reason)
})

bot.on('error', (err) => {
  console.log('Bot error:', err)
})

// Debug: log important packets
bot._client.on('packet', (data, meta) => {
  if (meta.name === 'resource_pack_send' ||
      meta.name === 'resource_pack_receive' ||
      meta.name === 'login_success' ||
      meta.name === 'join_game' ||
      meta.name === 'player_info') {
    console.log(`Received packet: ${meta.name}`, data)
  }
})

// Add timeout to detect if bot gets stuck
setTimeout(() => {
  if (!bot.entity) {
    console.log('Bot seems stuck - no entity after 30 seconds')
    console.log('Current bot state:', {
      loggedIn: !!bot.username,
      hasEntity: !!bot.entity,
      health: bot.health,
      gameMode: bot.game?.gameMode
    })
  }
}, 30000)