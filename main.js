const mineflayer = require('mineflayer')

const bot = mineflayer.createBot({
  host: 'mc.kartoplyaniy.space', // minecraft server ip
  username: '<PERSON><PERSON><PERSON>', // username to join as if auth is `offline`, else a unique identifier for this account. Switch if you want to change accounts
  auth: 'offline', // for offline mode servers, you can set this to 'offline'
  port: 25565, // default minecraft port
  version: false, // automatically detect server version
  skipValidation: true, // skip validation to avoid resource pack issues
  hideErrors: false, // show errors for debugging
  checkTimeoutInterval: 30000, // increase timeout
  keepAlive: true, // keep connection alive
  respawn: true // auto respawn if died
})

// Handle resource pack requests - accept them to avoid being kicked
bot.on('resourcePack', (url, hash) => {
  console.log('Server requested resource pack, accepting...')
  console.log('Resource pack URL:', url)
  console.log('Resource pack hash:', hash)

  // Accept the resource pack to avoid being kicked
  try {
    bot.acceptResourcePack()
    console.log('Resource pack accepted successfully')
  } catch (err) {
    console.log('Error accepting resource pack:', err.message)

    // Try alternative method - send raw packet
    try {
      bot._client.write('resource_pack_receive', {
        result: 0 // 0 = successfully downloaded
      })
      console.log('Sent resource pack status via raw packet')
    } catch (rawErr) {
      console.log('Raw packet method also failed:', rawErr.message)
    }
  }
})

// Handle resource pack download progress (if it happens)
bot.on('resourcePackProgress', (progress) => {
  console.log(`Resource pack download progress: ${progress}%`)
})

// Handle successful login
bot.on('login', () => {
  console.log(`Bot logged in as ${bot.username} to ${bot.options.host}`)
})

// Handle spawn event
bot.on('spawn', () => {
  console.log('Bot spawned in the world!')
})

bot.on('chat', (username, message) => {
  if (username === bot.username) return
  bot.chat(message)
})

// Log errors and kick reasons:
bot.on('kicked', (reason) => {
  console.log('Bot was kicked:', reason)
})

bot.on('error', (err) => {
  console.log('Bot error:', err)
})