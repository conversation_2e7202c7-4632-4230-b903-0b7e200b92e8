name: Update Helper
on:
  workflow_dispatch:
  schedule:
    - cron: "0 */2 * * *"

jobs:
  helper:
    name: update-checker
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@master
    - name: Set up Node.js
      uses: actions/setup-node@master
      with:
        node-version: 22.0.0
    - name: Install Github Actions toolkit
      run: npm i
      working-directory: .github/helper-bot
    # The env vars contain the relevant trigger information, so we don't need to pass it
    - name: Runs helper
      run: node index.js
      working-directory: .github/helper-bot
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
